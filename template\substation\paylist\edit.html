<!DOCTYPE html>
<html>
<head>
	{include file="common_header" /}
	{include file="common_top" /}
</head>
<body>

  <div class="layui-fluid">
    <div class="layui-card">
      <div class="layui-card-body" style="padding: 15px;">
        <form class="layui-form" action="" lay-filter="component-form-group">

        <div class="layui-form-item">
            <label class="layui-form-label">支付名称</label>
            <div class="layui-input-block">
              <input name="title" type="text" disabled class="layui-input" id="title" value="{$info.pl_title}" readonly="readonly"   placeholder="支付名称">
            </div>
          </div>   
          
 
       
          {volist name="list" id="vo" key="index"}
          <div class="layui-form-item">
            <label class="layui-form-label">{$vo}</label>
            <div class="layui-input-block">
            	<textarea name="su_pl_content_{$index}" id="su_pl_content_{$index}" class="layui-input"   placeholder="输入支付{$vo}开启多组帐号轮换支付插件请用-分开" rows="">{$SubPaylist['su_pl_content_'.$index]}</textarea>
            </div>
          </div> 
          {/volist}

          
          <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-block">
              <input type="radio" name="status" value="1" title="启用" {if $SubPaylist.su_pl_status==1}checked=""{/if}>
              <input type="radio" name="status" value="2" title="禁用" {if $SubPaylist.su_pl_status==2}checked=""{/if}>
            </div>
          </div>
          

		  
         <div class="layui-form-item layui-layout-admin">
              <div class="layui-footer" style="left: 0;">
                <div class="layui-btn sub">立即提交</div>
                <button type="reset" class="layui-btn layui-btn-primary ">重置</button>
              </div>
          </div>
        </form>
      </div>
    </div>
  </div>
{include file="common_footer" /} 

<script>
$(".sub").click(function(){
	//if(!$(".btn").hasClass("sub")){return false;}
	{volist name="list" id="vo" key="index"}
var su_pl_content_{$index} = $("#su_pl_content_{$index}").val();
	{/volist}
var status  = $("input[name='status']:checked").val();
	if(typeof(status) == "undefined"){
		show_error("状态不能为空，请先选择状态!");
		return false;
	}
	
	$.ajax({
		type:"POST",
		url:"{:url('paylist/edit')}",
		dataType:"json",
		data:{
			id:"{$info.pl_id}",
			status:status,
			{volist name="list" id="vo" key="index"}
su_pl_content_{$index}:su_pl_content_{$index},
			{/volist}
		},
		success:function(res){
			if(res.status == 1){
				window.parent.layer.closeAll();//关闭弹窗
				window.parent.location.reload();
				//show_toast_callurl(res.data,"{:url('device/index')}","success");
			}else{
				show_error(res.msg);
			}
		},
		error:function(jqXHR){
			console.log("Error: "+jqXHR.status);
		},
	});
	
});
</script>
</body>
</html>
