<!DOCTYPE html>
<html>
<head>
	{include file="common_header" /}
	{include file="common_top" /}
</head>
<body class="layui-layout-body">
  <div id="LAY_app">
    <div class="layui-layout layui-layout-admin">
      <div class="layui-header">
        <!-- 头部区域 -->
        <ul class="layui-nav layui-layout-left">
          <li class="layui-nav-item layadmin-flexible" lay-unselect>
            <a href="javascript:;" layadmin-event="flexible" title="侧边伸缩">
              <i class="layui-icon layui-icon-shrink-right" id="LAY_app_flexible"></i>
            </a>
          </li>
          <li class="layui-nav-item" lay-unselect>
            <a href="javascript:;" layadmin-event="refresh" title="刷新">
              <i class="layui-icon layui-icon-refresh-3"></i>
            </a>
          </li>
        </ul>
        <ul class="layui-nav layui-layout-right" lay-filter="layadmin-layout-right">
          <li class="layui-nav-item layui-hide-xs" lay-unselect>
            <a href="javascript:;" layadmin-event="fullscreen">
              <i class="layui-icon layui-icon-screen-full"></i>
            </a>
          </li>
          <li class="layui-nav-item" lay-unselect>
            <a href="javascript:;">
              <cite>{:session("su_name")}</cite>
            </a>
            <dl class="layui-nav-child">
              <dd><a lay-href="{:url("users/password")}">修改密码</a></dd>
              <hr>
              <dd style="text-align: center; position:cursor;"><a class="outlogin" href="javascript:void(0)">退出</a></dd>
            </dl>
          </li>
		 
        </ul>
      </div>
      
      <!-- 侧边菜单 -->
      <div class="layui-side layui-side-menu">
        <div class="layui-side-scroll">
          <div class="layui-logo" lay-href="{:url('center/home')}">
            <span>{$subweb['oaname']}</span>
          </div>
          
          <ul class="layui-nav layui-nav-tree" lay-shrink="all" id="LAY-system-side-menu" lay-filter="layadmin-system-side-menu">
            <li data-name="home" class="layui-nav-item layui-nav-itemed">
              <a href="javascript:;" lay-tips="主页" lay-direction="2">
                <i class="layui-icon layui-icon-home"></i>
                <cite>主页</cite>
              </a>
              <dl class="layui-nav-child">
                <dd data-name="console" class="layui-this">
                  <a lay-href="{:url('center/home')}">数据总览</a>
                </dd>
              </dl>
            </li>
            		<li data-name="内容管理" class="layui-nav-item">
						<a href="javascript:;" lay-href="{:url('wxgroup/index')}" lay-tips="内容管理" lay-direction="2">
							<i class="layui-icon layui-icon-auz"></i>
							<cite>内容管理</cite>
						</a>
					</li>
					
					<li data-name="内容模板" class="layui-nav-item">
						<a href="javascript:;" lay-href="{:url('wxgrouptmp/index')}" lay-tips="内容模板" lay-direction="2">
							<i class="layui-icon layui-icon-auz"></i>
							<cite>内容模板</cite>
						</a>
					</li>
            
                    <!--<li data-name="多群管理" class="layui-nav-item">
						<a href="javascript:;" lay-href="{:url('dwxgroup/index')}" lay-tips="多群管理" lay-direction="2">
							<i class="layui-icon layui-icon-auz"></i>
							<cite>多群管理</cite>
						</a>
					</li>-->
            
            		<li data-name="分销会员" class="layui-nav-item">
						<a href="javascript:;" lay-href="{:url('distribution/index')}" lay-tips="分销会员" lay-direction="2">
							<i class="layui-icon layui-icon-auz"></i>
							<cite>分销会员</cite>
						</a>
					</li>
            
            		<li data-name="分销群组" class="layui-nav-item">
						<a href="javascript:;" lay-href="{:url('distributiongroup/index')}" lay-tips="分销群组" lay-direction="2">
							<i class="layui-icon layui-icon-auz"></i>
							<cite>分销群组</cite>
						</a>
					</li>
					
					
					{if $fzinfo.su_fzonoff==2}
					<li data-name="子分站管理" class="layui-nav-item">
						<a href="javascript:;" lay-href="{:url('substation/index')}" lay-tips="子分站管理" lay-direction="2">
							<i class="layui-icon layui-icon-auz"></i>
							<cite>子分站管理</cite>
						</a>
					</li>
					
					<li data-name="抽拥记录" class="layui-nav-item">
						<a href="javascript:;" lay-href="{:url('chouyong/index')}" lay-tips="抽拥记录" lay-direction="2">
							<i class="layui-icon layui-icon-auz"></i>
							<cite>抽佣记录</cite>
						</a>
					</li>
					
					<li data-name="抽拥记录" class="layui-nav-item">
						<a href="javascript:;" lay-href="{:url('chouyongtixian/index')}" lay-tips="抽拥记录" lay-direction="2">
							<i class="layui-icon layui-icon-auz"></i>
							<cite>佣金提现</cite>
						</a>
					</li>
					
					{/if}
                    
                    <li data-name="帐单记录" class="layui-nav-item">
						<a href="javascript:;" lay-href="{:url('bill/index')}" lay-tips="帐单记录" lay-direction="2">
							<i class="layui-icon layui-icon-auz"></i>
							<cite>帐单记录</cite>
						</a>
					</li>
					
					<li data-name="点卡记录" class="layui-nav-item">
						<a href="javascript:;" lay-href="{:url('dianka/index')}" lay-tips="点卡记录" lay-direction="2">
							<i class="layui-icon layui-icon-auz"></i>
							<cite>点卡记录</cite>
						</a>
					</li>
					
					<li data-name="扣点记录" class="layui-nav-item">
						<a href="javascript:;" lay-href="{:url('diankalog/index')}" lay-tips="扣点记录" lay-direction="2">
							<i class="layui-icon layui-icon-auz"></i>
							<cite>扣点记录</cite>
						</a>
					</li>
					
					<li data-name="对帐管理" class="layui-nav-item">
						<a href="javascript:;" lay-href="{:url('duizhang/index')}" lay-tips="对帐管理" lay-direction="2">
							<i class="layui-icon layui-icon-auz"></i>
							<cite>对帐管理</cite>
						</a>
					</li>
					
                    
                    <li data-name="提现管理" class="layui-nav-item">
						<a href="javascript:;" lay-href="{:url('distributiontixian/index')}" lay-tips="提现管理" lay-direction="2">
							<i class="layui-icon layui-icon-auz"></i>
							<cite>提现管理</cite>
						</a>
					</li>
		
					<li data-name="支付设置" class="layui-nav-item">
						<a href="javascript:;" lay-href="{:url('paylist/index')}" lay-tips="支付设置" lay-direction="2">
							<i class="layui-icon layui-icon-auz"></i>
							<cite>支付设置</cite>
						</a>
					</li>

			
          </ul>
        </div>
      </div>

      <!-- 页面标签 -->
      <div class="layadmin-pagetabs" id="LAY_app_tabs">
        <div class="layui-icon layadmin-tabs-control layui-icon-prev" layadmin-event="leftPage"></div>
        <div class="layui-icon layadmin-tabs-control layui-icon-next" layadmin-event="rightPage"></div>
        <div class="layui-icon layadmin-tabs-control layui-icon-down">
          <ul class="layui-nav layadmin-tabs-select" lay-filter="layadmin-pagetabs-nav">
            <li class="layui-nav-item" lay-unselect>
              <a href="javascript:;"></a>
              <dl class="layui-nav-child layui-anim-fadein">
                <dd layadmin-event="closeThisTabs"><a href="javascript:;">关闭当前标签页</a></dd>
                <dd layadmin-event="closeOtherTabs"><a href="javascript:;">关闭其它标签页</a></dd>
                <dd layadmin-event="closeAllTabs"><a href="javascript:;">关闭全部标签页</a></dd>
              </dl>
            </li>
          </ul>
        </div>
        <div class="layui-tab" lay-unauto lay-allowClose="true" lay-filter="layadmin-layout-tabs">
          <ul class="layui-tab-title" id="LAY_app_tabsheader">
            <li lay-id="{:url('center/home')}" lay-attr="{:url('center/home')}" class="layui-this"><i class="layui-icon layui-icon-home"></i></li>
          </ul>
        </div>
      </div>

      <!-- 主体内容 -->
      <div class="layui-body" id="LAY_app_body">
        <div class="layadmin-tabsbody-item layui-show">
          <iframe src="{:url('center/home')}" frameborder="0" class="layadmin-iframe"></iframe>
        </div>
      </div>
      
      <!-- 辅助元素，一般用于移动设备下遮罩 -->
      <div class="layadmin-body-shade" layadmin-event="shade"></div>
    </div>
  </div>
	{include file="common_footer" /}
	<script>
	$(".outlogin").click(function(){
		$.ajax({
			type:"POST",
			url:"{:url('index/outlogin')}",
			dataType:"json",
			data:{},
			success:function(res){
				if(res.status == "success"){
					layer.msg(res.data, {offset: '15px',icon: 1,time: 1000}, function(){
						location.href = '{:url('index/Index')}';
					});
				}else{
					layer.msg(res.data, {offset: '15px',icon: 2});
				}
			},
			error:function(jqXHR){
				console.log("Error: "+jqXHR.status);
			},
		});
		
	});
  </script>
</body>
</html>