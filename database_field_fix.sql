-- ========================================
-- 数据库字段修复脚本
-- 修复分站表和分销表的字段不匹配问题
-- ========================================

SET NAMES utf8mb4;

SELECT '开始修复数据库字段...' as '修复状态';

-- ========================================
-- 分站表字段修复
-- ========================================

-- 检查并添加缺失的分站表字段
SELECT '修复分站表字段...' as '当前操作';

-- 添加分站资金字段
ALTER TABLE `web_substation` ADD COLUMN IF NOT EXISTS `su_fz_money` decimal(10,2) DEFAULT 0.00 COMMENT '分站资金';

-- 添加上级分站ID字段
ALTER TABLE `web_substation` ADD COLUMN IF NOT EXISTS `su_s_id` int(11) DEFAULT 0 COMMENT '上级分站ID';

-- 添加到期时间字段
ALTER TABLE `web_substation` ADD COLUMN IF NOT EXISTS `su_endtime` date DEFAULT NULL COMMENT '到期时间';

-- 添加分站账号别名字段（用于兼容代码中的su_name）
ALTER TABLE `web_substation` ADD COLUMN IF NOT EXISTS `su_name` varchar(50) DEFAULT '' COMMENT '分站账号别名';

-- 添加分站密码别名字段（用于兼容代码中的su_pass）
ALTER TABLE `web_substation` ADD COLUMN IF NOT EXISTS `su_pass` varchar(64) DEFAULT '' COMMENT '分站密码别名';

-- 添加点卡余额字段
ALTER TABLE `web_substation` ADD COLUMN IF NOT EXISTS `su_dk` decimal(10,2) DEFAULT 0.00 COMMENT '点卡余额';

-- 添加抽佣比例字段
ALTER TABLE `web_substation` ADD COLUMN IF NOT EXISTS `su_dk_cd` decimal(5,2) DEFAULT 0.00 COMMENT '抽佣比例';

-- 同步现有数据到别名字段
UPDATE `web_substation` SET `su_name` = `su_username` WHERE `su_name` = '' OR `su_name` IS NULL;
UPDATE `web_substation` SET `su_pass` = `su_password` WHERE `su_pass` = '' OR `su_pass` IS NULL;

-- ========================================
-- 分销表字段修复
-- ========================================

SELECT '修复分销表字段...' as '当前操作';

-- 添加分销账号别名字段（用于兼容代码中的du_name）
ALTER TABLE `web_distribution` ADD COLUMN IF NOT EXISTS `du_name` varchar(50) DEFAULT '' COMMENT '分销账号别名';

-- 添加分销密码别名字段（用于兼容代码中的du_pass）
ALTER TABLE `web_distribution` ADD COLUMN IF NOT EXISTS `du_pass` varchar(64) DEFAULT '' COMMENT '分销密码别名';

-- 添加分销ID别名字段（用于兼容代码中的du_id）
ALTER TABLE `web_distribution` ADD COLUMN IF NOT EXISTS `du_id` int(11) DEFAULT 0 COMMENT '分销ID别名';

-- 添加所属分站ID别名字段（用于兼容代码中的su_id）
ALTER TABLE `web_distribution` ADD COLUMN IF NOT EXISTS `su_id` int(11) DEFAULT 0 COMMENT '所属分站ID别名';

-- 添加分销状态别名字段（用于兼容代码中的du_status）
ALTER TABLE `web_distribution` ADD COLUMN IF NOT EXISTS `du_status` tinyint(1) DEFAULT 1 COMMENT '分销状态别名';

-- 同步现有数据到别名字段
UPDATE `web_distribution` SET `du_name` = `di_username` WHERE `du_name` = '' OR `du_name` IS NULL;
UPDATE `web_distribution` SET `du_pass` = `di_password` WHERE `du_pass` = '' OR `du_pass` IS NULL;
UPDATE `web_distribution` SET `du_id` = `di_id` WHERE `du_id` = 0 OR `du_id` IS NULL;
UPDATE `web_distribution` SET `su_id` = `di_su_id` WHERE `su_id` = 0 OR `su_id` IS NULL;
UPDATE `web_distribution` SET `du_status` = `di_status` WHERE `du_status` IS NULL;

-- ========================================
-- 微信群表修复
-- ========================================

SELECT '检查微信群表...' as '当前操作';

-- 创建微信群表（如果不存在）
CREATE TABLE IF NOT EXISTS `web_wxgroup` (
  `wxg_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '微信群ID',
  `su_id` int(11) NOT NULL DEFAULT 0 COMMENT '所属分站ID',
  `du_id` int(11) NOT NULL DEFAULT 0 COMMENT '所属分销ID',
  `wxg_title` varchar(100) NOT NULL COMMENT '群名称',
  `wxg_qrcode` varchar(255) DEFAULT '' COMMENT '群二维码',
  `wxg_count` int(11) DEFAULT 0 COMMENT '群人数',
  `wxg_max_count` int(11) DEFAULT 500 COMMENT '群最大人数',
  `wxg_status` tinyint(1) DEFAULT 1 COMMENT '状态 1:正常 2:满员 3:禁用',
  `wxg_addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `wxg_updatetime` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`wxg_id`),
  KEY `idx_su_id` (`su_id`),
  KEY `idx_du_id` (`du_id`),
  KEY `idx_status` (`wxg_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信群表';

-- 创建微信群模板表（如果不存在）
CREATE TABLE IF NOT EXISTS `web_wxgroup_tmp` (
  `wxgt_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `su_id` int(11) NOT NULL DEFAULT 0 COMMENT '所属分站ID',
  `wxgt_title` varchar(100) NOT NULL COMMENT '模板名称',
  `wxgt_content` text COMMENT '模板内容',
  `wxgt_status` tinyint(1) DEFAULT 1 COMMENT '状态',
  `wxgt_addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  PRIMARY KEY (`wxgt_id`),
  KEY `idx_su_id` (`su_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信群模板表';

-- ========================================
-- 账单表修复
-- ========================================

SELECT '检查账单表...' as '当前操作';

-- 为账单表添加缺失字段（如果需要）
ALTER TABLE `web_bill` ADD COLUMN IF NOT EXISTS `su_id` int(11) DEFAULT 0 COMMENT '所属分站ID';
ALTER TABLE `web_bill` ADD COLUMN IF NOT EXISTS `du_id` int(11) DEFAULT 0 COMMENT '所属分销ID';
ALTER TABLE `web_bill` ADD COLUMN IF NOT EXISTS `wxg_id` int(11) DEFAULT 0 COMMENT '所属微信群ID';
ALTER TABLE `web_bill` ADD COLUMN IF NOT EXISTS `bl_status` tinyint(1) DEFAULT 1 COMMENT '账单状态';
ALTER TABLE `web_bill` ADD COLUMN IF NOT EXISTS `bl_money` decimal(10,2) DEFAULT 0.00 COMMENT '账单金额';
ALTER TABLE `web_bill` ADD COLUMN IF NOT EXISTS `bl_scalemoney` decimal(10,2) DEFAULT 0.00 COMMENT '分成金额';
ALTER TABLE `web_bill` ADD COLUMN IF NOT EXISTS `bl_addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间';

-- ========================================
-- 添加必要的索引
-- ========================================

SELECT '添加索引优化...' as '当前操作';

-- 分站表索引
ALTER TABLE `web_substation` ADD INDEX IF NOT EXISTS `idx_su_name_status` (`su_name`, `su_status`);
ALTER TABLE `web_substation` ADD INDEX IF NOT EXISTS `idx_su_s_id` (`su_s_id`);
ALTER TABLE `web_substation` ADD INDEX IF NOT EXISTS `idx_su_endtime` (`su_endtime`);

-- 分销表索引
ALTER TABLE `web_distribution` ADD INDEX IF NOT EXISTS `idx_du_name_status` (`du_name`, `du_status`);
ALTER TABLE `web_distribution` ADD INDEX IF NOT EXISTS `idx_su_id_status` (`su_id`, `du_status`);

-- 微信群表索引
ALTER TABLE `web_wxgroup` ADD INDEX IF NOT EXISTS `idx_su_du_status` (`su_id`, `du_id`, `wxg_status`);

-- 账单表索引
ALTER TABLE `web_bill` ADD INDEX IF NOT EXISTS `idx_su_du_wxg` (`su_id`, `du_id`, `wxg_id`);
ALTER TABLE `web_bill` ADD INDEX IF NOT EXISTS `idx_status_time` (`bl_status`, `bl_addtime`);

SELECT '数据库字段修复完成！' as '修复状态';

-- ========================================
-- 验证修复结果
-- ========================================

SELECT '验证修复结果...' as '当前操作';

-- 检查分站表字段
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'web_substation' 
    AND COLUMN_NAME IN ('su_fz_money', 'su_s_id', 'su_endtime', 'su_name', 'su_pass', 'su_dk', 'su_dk_cd')
ORDER BY ORDINAL_POSITION;

-- 检查分销表字段
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'web_distribution' 
    AND COLUMN_NAME IN ('du_name', 'du_pass', 'du_id', 'su_id', 'du_status')
ORDER BY ORDINAL_POSITION;

SELECT '字段修复验证完成！请检查上述查询结果确认字段已正确添加。' as '验证结果';
