<!DOCTYPE html>
<html>
<head>
	{include file="common_header" /}
	{include file="common_top" /}
</head>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-body">
          	<div class="layui-box">
			{if($__APS__.add)}<button class="layui-btn layuiadmin-btn-tags" data-type="add">添加</button>{/if}
			 </div>
			<div class="layui-box layui-laypage layui-laypage-molv">{$page}</div>
            <table class="layui-table" lay-even="" lay-skin="nob">
            <thead>
              <tr>
				<th width="20"></th>
                <th width="35">序号</th>
				<th width="35">状态</th>
				<th width="60">用户数</th>
				<th>群组名称</th>
				{if($__APS__.del or $__APS__.edit)}<th width="170">管理</th>{/if}
              </tr> 
            </thead>
            <tbody>
             {volist name="list" id="vo"}
				<tr id="tr_{$vo.id}">
					<td><input type="checkbox" name="delmulti" id="checkbox" value="{$vo.id}"></td>
					<td class="text-center">{$vo.id}</td>
					<td class="text-center">{if $vo.status==1}<span class="layui-badge-dot layui-bg-green"></span>{else}<span class="layui-badge-dot"></span>{/if}</td>
					<td class="text-center">{$vo.count}</td>
					<td>{$vo.title}</td>
					{if($__APS__.del or $__APS__.edit)}
					<td>
							<div class="layui-btn-group">
								{if($__APS__.del)}<button class="layui-btn layui-btn-sm" onClick="calldel('{:url('group/del',array('id'=>$vo.id))}','tr_{$vo.id}')"><i class="layui-icon">&#xe640;</i></button>{/if}
								{if($__APS__.add)}<button class="layui-btn layui-btn-sm" data-type="edit" data-id="{$vo.id}"><i class="layui-icon">&#xe642;</i></button>{/if}
								{if($__APS__.addauth)}<button class="layui-btn layui-btn-sm" data-type="addauth" data-id="{$vo.id}"><i class="layui-icon">&#xe624;</i></button>{/if}
								{if($__APS__.addnavigat)}<button class="layui-btn layui-btn-sm" data-type="addnavigat" data-id="{$vo.id}"><i class="layui-icon">&#xe632;</i></button>{/if}
							</div>
					</td>
					{/if}
				</tr>
			{/volist}
            </tbody>
          </table> 
		  <div class="layui-box layui-laypage layui-laypage-molv">{$page}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  {include file="common_footer" /} 
   <script>
  layui.use(['index'], function(){
    var $ = layui.$, active = {
		{if($__APS__.add)}
      add: function(){
        layer.open({
          type: 2
          ,title: '添加群组'
          ,content: '{:url('group/add')}'
          ,area: ['550px', '290px']
        }); 
      },
	  {/if}
	  
	  {if($__APS__.edit)}
	  edit: function(){
		var id = $(this).data('id');
		var url = '{:url('group/edit',array('id'=>'AAAAAA'))}';
		url = url.replace("AAAAAA",id)
        layer.open({
          type: 2
          ,title: '修改群组'
          ,content: url
          ,area: ['550px', '290px']
        }); 
      },
	  {/if}
	  
	  {if($__APS__.addauth)}
	  addauth: function(){
		var id = $(this).data('id');
		var url = '{:url('group/addauth',array('id'=>'AAAAAA'))}';
		url = url.replace("AAAAAA",id)
        layer.open({
          type: 2
          ,title: '群组权限设置'
          ,content: url
          ,area: ['890px', '590px']
        }); 
      },
	  {/if}
	  
	  {if($__APS__.addnavigat)}
	  addnavigat: function(){
		var id = $(this).data('id');
		var url = '{:url('group/addnavigat',array('id'=>'AAAAAA'))}';
		url = url.replace("AAAAAA",id)
        layer.open({
          type: 2
          ,title: '设置群组导航'
          ,content: url
          ,area: ['890px', '590px']
        }); 
      },
	  {/if} 
	  

    } 
	
	
    $('.layui-btn').on('click', function(){
      var type = $(this).data('type');
      active[type] ? active[type].call(this) : '';
    });

	
  });
  </script>
</body>
</html>