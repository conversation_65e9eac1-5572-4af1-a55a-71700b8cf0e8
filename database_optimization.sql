-- ========================================
-- 数据库性能优化脚本（修正版）
-- 仅包含与初始化脚本匹配的索引优化
-- ========================================

SET NAMES utf8mb4;

SELECT '开始数据库性能优化...' as '优化状态';

-- 用户表索引优化
ALTER TABLE `web_users` ADD INDEX `idx_phone_status` (`u_phone`, `u_status`);
ALTER TABLE `web_users` ADD INDEX `idx_regtime` (`u_regtime`);
ALTER TABLE `web_users` ADD INDEX `idx_last_time` (`u_last_time`);

-- 用户登录日志表索引优化
ALTER TABLE `web_users_logs` ADD INDEX `idx_uid_type_time` (`u_id`, `ul_type`, `ul_addtime`);
ALTER TABLE `web_users_logs` ADD INDEX `idx_ip_time` (`ul_ip`, `ul_addtime`);

-- 权限表索引优化
ALTER TABLE `web_auth_rule` ADD INDEX `idx_status_said_sort` (`status`, `said`, `sort`);
ALTER TABLE `web_auth_group` ADD INDEX `idx_status_title` (`status`, `title`);

-- 分站表索引优化
ALTER TABLE `web_substation` ADD INDEX `idx_username_status` (`su_username`, `su_status`);
ALTER TABLE `web_substation` ADD INDEX `idx_addtime_status` (`su_addtime`, `su_status`);
ALTER TABLE `web_substation` ADD INDEX `idx_group_status` (`su_g_id`, `su_status`);

-- 分站群组表索引优化
ALTER TABLE `web_substationgroup` ADD INDEX `idx_title_addtime` (`su_g_title`, `su_g_addtime`);

-- 分销表索引优化
ALTER TABLE `web_distribution` ADD INDEX `idx_username_status` (`di_username`, `di_status`);
ALTER TABLE `web_distribution` ADD INDEX `idx_addtime_status` (`di_addtime`, `di_status`);
ALTER TABLE `web_distribution` ADD INDEX `idx_su_status` (`di_su_id`, `di_status`);

-- 分销群组表索引优化
ALTER TABLE `web_distributiongroup` ADD INDEX `idx_title_addtime` (`di_g_title`, `di_g_addtime`);

-- 分销提现表索引优化
ALTER TABLE `web_distributiontixian` ADD INDEX `idx_amount_status` (`dt_amount`, `dt_status`);
ALTER TABLE `web_distributiontixian` ADD INDEX `idx_handle_time` (`dt_handle_time`);

-- 微信群组表索引优化
ALTER TABLE `web_wxgroup` ADD INDEX `idx_title_status` (`wg_title`, `wg_status`);
ALTER TABLE `web_wxgroup` ADD INDEX `idx_addtime_status` (`wg_addtime`, `wg_status`);

-- 微信群模板表索引优化
ALTER TABLE `web_wxgrouptmp` ADD INDEX `idx_title_status` (`wt_title`, `wt_status`);
ALTER TABLE `web_wxgrouptmp` ADD INDEX `idx_addtime` (`wt_addtime`);

-- 账单表索引优化
ALTER TABLE `web_bill` ADD INDEX `idx_user_type_id` (`bi_user_type`, `bi_user_id`);
ALTER TABLE `web_bill` ADD INDEX `idx_amount_type` (`bi_amount`, `bi_type`);
ALTER TABLE `web_bill` ADD INDEX `idx_addtime_type` (`bi_addtime`, `bi_type`);

-- 支付表索引优化
ALTER TABLE `web_paylist` ADD INDEX `idx_title_status` (`pl_title`, `pl_status`);
ALTER TABLE `web_paylist` ADD INDEX `idx_addtime` (`pl_addtime`);

-- 点卡表索引优化
ALTER TABLE `web_dianka` ADD INDEX `idx_amount_status` (`dk_amount`, `dk_status`);
ALTER TABLE `web_dianka` ADD INDEX `idx_use_time` (`dk_use_time`);

-- 点卡日志表索引优化
ALTER TABLE `web_diankalog` ADD INDEX `idx_dk_id_time` (`dl_dk_id`, `dl_addtime`);
ALTER TABLE `web_diankalog` ADD INDEX `idx_user_type_id` (`dl_user_type`, `dl_user_id`);

-- 财务相关表索引优化
ALTER TABLE `web_chouyong` ADD INDEX `idx_user_type_id` (`cy_user_type`, `cy_user_id`);
ALTER TABLE `web_chouyong` ADD INDEX `idx_addtime` (`cy_addtime`);

ALTER TABLE `web_duizhang` ADD INDEX `idx_user_type_id` (`dz_user_type`, `dz_user_id`);
ALTER TABLE `web_duizhang` ADD INDEX `idx_addtime_type` (`dz_addtime`, `dz_type`);

-- 分站提现表索引优化
ALTER TABLE `web_substationtixian` ADD INDEX `idx_amount_status` (`st_amount`, `st_status`);
ALTER TABLE `web_substationtixian` ADD INDEX `idx_handle_time` (`st_handle_time`);

-- 系统表索引优化
ALTER TABLE `web_navigat` ADD INDEX `idx_title_status` (`nav_title`, `nav_status`);
ALTER TABLE `web_navigat` ADD INDEX `idx_sort_status` (`nav_sort`, `nav_status`);

ALTER TABLE `web_voicelist` ADD INDEX `idx_title_status` (`vol_title`, `vol_status`);
ALTER TABLE `web_voicelist` ADD INDEX `idx_addtime_status` (`vol_addtime`, `vol_status`);

ALTER TABLE `web_operation_logs` ADD INDEX `idx_user_type_id_time` (`ol_user_type`, `ol_user_id`, `ol_addtime`);
ALTER TABLE `web_operation_logs` ADD INDEX `idx_module_controller` (`ol_module`, `ol_controller`);

SELECT '数据库索引优化完成！' as '优化状态';
SELECT '建议定期执行 ANALYZE TABLE 维护索引性能' as '维护建议';