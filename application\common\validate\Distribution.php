<?php
namespace app\common\validate;
use think\Validate;

class Distribution extends Validate
{
    protected $rule = [
        // 登录验证规则
        'username'  => 'require|alphaNum|min:3|max:20',
        'password'  => 'require|min:6|max:32',
        
        // 添加/编辑分销验证规则
        'di_title'    => 'require|min:2|max:50',
        'di_g_id'     => 'require|integer|gt:0',
        'di_su_id'    => 'require|integer|gt:0',
        'di_username' => 'require|alphaNum|min:3|max:20',
        'di_password' => 'require|min:6|max:32',
        'di_status'   => 'in:1,2',
        
        // 密码修改验证规则
        'oldpassword' => 'require|min:6|max:32',
        'newpassword' => 'require|min:6|max:32|different:oldpassword',
        'endpassword' => 'require|confirm:newpassword',
    ];

    protected $message = [
        // 登录验证消息
        'username.require'   => '用户名必须填写',
        'username.alphaNum'  => '用户名只能是字母和数字',
        'username.min'       => '用户名长度不能少于3位',
        'username.max'       => '用户名长度不能超过20位',
        'password.require'   => '密码必须填写',
        'password.min'       => '密码长度不能少于6位',
        'password.max'       => '密码长度不能超过32位',
        
        // 添加/编辑分销验证消息
        'di_title.require'    => '分销名称必须填写',
        'di_title.min'        => '分销名称长度不能少于2位',
        'di_title.max'        => '分销名称长度不能超过50位',
        'di_g_id.require'     => '分销分组必须选择',
        'di_g_id.integer'     => '分销分组ID必须是整数',
        'di_g_id.gt'          => '分销分组ID必须大于0',
        'di_su_id.require'    => '所属分站必须选择',
        'di_su_id.integer'    => '分站ID必须是整数',
        'di_su_id.gt'         => '分站ID必须大于0',
        'di_username.require' => '分销账号必须填写',
        'di_username.alphaNum' => '分销账号只能是字母和数字',
        'di_username.min'     => '分销账号长度不能少于3位',
        'di_username.max'     => '分销账号长度不能超过20位',
        'di_password.require' => '分销密码必须填写',
        'di_password.min'     => '分销密码长度不能少于6位',
        'di_password.max'     => '分销密码长度不能超过32位',
        'di_status.in'        => '分销状态值不正确',
        
        // 密码修改验证消息
        'oldpassword.require' => '原密码必须填写',
        'oldpassword.min'     => '原密码长度不能少于6位',
        'oldpassword.max'     => '原密码长度不能超过32位',
        'newpassword.require' => '新密码必须填写',
        'newpassword.min'     => '新密码长度不能少于6位',
        'newpassword.max'     => '新密码长度不能超过32位',
        'newpassword.different' => '新密码不能与原密码相同',
        'endpassword.require' => '确认密码必须填写',
        'endpassword.confirm' => '确认密码与新密码不一致',
    ];

    protected $scene = [
        'login'  => ['username', 'password'],
        'add'    => ['di_title', 'di_g_id', 'di_su_id', 'di_username', 'di_password'],
        'edit'   => ['di_title', 'di_g_id', 'di_su_id', 'di_username', 'di_status'],
        'changePassword' => ['oldpassword', 'newpassword', 'endpassword'],
    ];

    /**
     * 自定义验证规则：检查用户名是否已存在
     */
    protected function checkUsernameUnique($value, $rule, $data = [])
    {
        $model = new \app\common\model\Distribution();
        $where = ['di_username' => $value];
        
        // 编辑时排除当前记录
        if (isset($data['di_id']) && $data['di_id'] > 0) {
            $where['di_id'] = ['neq', $data['di_id']];
        }
        
        $exists = $model->where($where)->find();
        return $exists ? false : true;
    }

    /**
     * 自定义验证规则：检查分站是否存在
     */
    protected function checkSubstationExists($value, $rule, $data = [])
    {
        $model = new \app\common\model\Substation();
        $exists = $model->where(['su_id' => $value, 'su_status' => 1])->find();
        return $exists ? true : false;
    }
}
