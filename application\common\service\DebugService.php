<?php
namespace app\common\service;

use think\Log;
use think\Request;
use think\Db;

/**
 * 调试服务类
 * 用于记录系统调试信息和操作日志
 */
class DebugService
{
    /**
     * 记录重定向调试信息
     * @param \think\Response $response 响应对象
     * @param array $data 额外数据
     */
    public static function logRedirect($response, $data = [])
    {
        try {
            $request = Request::instance();
            
            $logData = [
                'type' => 'redirect',
                'url' => $request->url(true),
                'method' => $request->method(),
                'ip' => $request->ip(),
                'user_agent' => $request->header('user-agent'),
                'redirect_url' => $response->getTargetUrl(),
                'session_id' => session_id(),
                'timestamp' => date('Y-m-d H:i:s'),
                'extra_data' => $data
            ];
            
            // 记录到日志文件
            Log::record('Redirect Debug: ' . json_encode($logData, JSON_UNESCAPED_UNICODE), 'debug');
            
            // 如果需要，也可以记录到数据库
            self::saveToDatabase('redirect', $logData);
            
        } catch (\Exception $e) {
            // 调试记录失败不应该影响正常流程
            Log::record('Debug log failed: ' . $e->getMessage(), 'error');
        }
    }
    
    /**
     * 记录操作日志
     * @param string $action 操作类型
     * @param array $data 操作数据
     * @param int $userId 用户ID
     * @param string $userType 用户类型 (admin/substation/distribution)
     */
    public static function logOperation($action, $data = [], $userId = 0, $userType = 'admin')
    {
        try {
            $request = Request::instance();
            
            $logData = [
                'ol_user_type' => $userType,
                'ol_user_id' => $userId,
                'ol_action' => $action,
                'ol_module' => $request->module(),
                'ol_controller' => $request->controller(),
                'ol_method' => $request->action(),
                'ol_url' => $request->url(true),
                'ol_ip' => $request->ip(),
                'ol_user_agent' => $request->header('user-agent'),
                'ol_data' => json_encode($data, JSON_UNESCAPED_UNICODE),
                'ol_addtime' => date('Y-m-d H:i:s')
            ];
            
            // 保存到操作日志表
            Db::name('operation_logs')->insert($logData);
            
        } catch (\Exception $e) {
            Log::record('Operation log failed: ' . $e->getMessage(), 'error');
        }
    }
    
    /**
     * 记录登录日志
     * @param int $userId 用户ID
     * @param string $userType 用户类型
     * @param string $result 登录结果 (success/failed)
     * @param string $message 消息
     */
    public static function logLogin($userId, $userType, $result, $message = '')
    {
        try {
            $request = Request::instance();
            
            $logData = [
                'll_user_type' => $userType,
                'll_user_id' => $userId,
                'll_result' => $result,
                'll_message' => $message,
                'll_ip' => $request->ip(),
                'll_user_agent' => $request->header('user-agent'),
                'll_addtime' => date('Y-m-d H:i:s')
            ];
            
            // 保存到登录日志表
            Db::name('login_logs')->insert($logData);
            
        } catch (\Exception $e) {
            Log::record('Login log failed: ' . $e->getMessage(), 'error');
        }
    }
    
    /**
     * 记录SQL查询调试信息
     * @param string $sql SQL语句
     * @param array $params 参数
     * @param float $time 执行时间
     */
    public static function logSql($sql, $params = [], $time = 0)
    {
        if (config('app_debug')) {
            $logData = [
                'sql' => $sql,
                'params' => $params,
                'time' => $time,
                'timestamp' => date('Y-m-d H:i:s')
            ];
            
            Log::record('SQL Debug: ' . json_encode($logData, JSON_UNESCAPED_UNICODE), 'sql');
        }
    }
    
    /**
     * 保存调试信息到数据库
     * @param string $type 类型
     * @param array $data 数据
     */
    private static function saveToDatabase($type, $data)
    {
        try {
            // 检查是否存在调试日志表
            $tableExists = Db::query("SHOW TABLES LIKE 'web_debug_logs'");
            
            if (empty($tableExists)) {
                // 创建调试日志表
                self::createDebugTable();
            }
            
            $logData = [
                'dl_type' => $type,
                'dl_data' => json_encode($data, JSON_UNESCAPED_UNICODE),
                'dl_addtime' => date('Y-m-d H:i:s')
            ];
            
            Db::name('debug_logs')->insert($logData);
            
        } catch (\Exception $e) {
            // 数据库记录失败，只记录到文件日志
            Log::record('Database debug log failed: ' . $e->getMessage(), 'error');
        }
    }
    
    /**
     * 创建调试日志表
     */
    private static function createDebugTable()
    {
        $sql = "
        CREATE TABLE IF NOT EXISTS `web_debug_logs` (
            `dl_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '调试日志ID',
            `dl_type` varchar(50) NOT NULL COMMENT '日志类型',
            `dl_data` text COMMENT '日志数据',
            `dl_addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
            PRIMARY KEY (`dl_id`),
            KEY `idx_type_time` (`dl_type`, `dl_addtime`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='调试日志表';
        ";
        
        Db::execute($sql);
    }
    
    /**
     * 清理过期的调试日志
     * @param int $days 保留天数，默认30天
     */
    public static function cleanOldLogs($days = 30)
    {
        try {
            $expireDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
            
            // 清理调试日志
            Db::name('debug_logs')->where('dl_addtime', '<', $expireDate)->delete();
            
            // 清理操作日志
            Db::name('operation_logs')->where('ol_addtime', '<', $expireDate)->delete();
            
            // 清理登录日志
            Db::name('login_logs')->where('ll_addtime', '<', $expireDate)->delete();
            
            Log::record("Cleaned debug logs older than {$days} days", 'info');
            
        } catch (\Exception $e) {
            Log::record('Clean old logs failed: ' . $e->getMessage(), 'error');
        }
    }
}
