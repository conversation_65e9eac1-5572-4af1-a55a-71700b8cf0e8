<!DOCTYPE html>
<html>
<head>
	{include file="common_header" /}
	{include file="common_top" /}
</head>
<body>

  <div class="layui-fluid">
    <div class="layui-card">
      <div class="layui-card-body" style="padding: 15px;">
        <form class="layui-form" action="" lay-filter="component-form-group">

        <div class="layui-form-item">
            <label class="layui-form-label">支付名称</label>
            <div class="layui-input-block">
              <input name="title" type="text" class="layui-input" id="title"   placeholder="支付名称" value="{$info.pl_title}">
            </div>
          </div>   
          
          <div class="layui-form-item">
            <label class="layui-form-label">支付编码</label>
            <div class="layui-input-block">
              <input name="code" type="text" class="layui-input" id="code"   placeholder="支付编码" value="{$info.pl_code}">
            </div>
          </div> 
          
		<div class="layui-form-item">
            <label class="layui-form-label">支付图标</label>
			<input type="hidden" id="ico" name="ico" value="{$info.pl_ico}">
            <div class="layui-input-block">
 <div class="layui-upload">
  <button type="button" class="layui-btn" id="test1">上传图片</button>
  <div class="layui-upload-list">
    <img class="layui-upload-img" id="demo1" style="width:90px; height:90px;" src="{$info.pl_ico}">
    <p id="demoText"></p>
  </div>
</div>  
            </div>
          </div>
          
          <div class="layui-form-item">
            <label class="layui-form-label">支付网址</label>
            <div class="layui-input-block">
              <input name="url" type="text" class="layui-input" id="url"   placeholder="支付网址" value="{$info.pl_url}">
            </div>
          </div> 
          
          <div class="layui-form-item">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-block">
              <input name="content" type="text" class="layui-input" id="content"   placeholder="备注" value="{$info.pl_content}">
            </div>
          </div> 
          
          <div class="layui-form-item">
            <label class="layui-form-label">参数说明</label>
            <div class="layui-input-block">
            <textarea name="actname" id="actname" class="layui-input"  cols="" rows="">{$info.pl_actname}</textarea>
            </div>
          </div> 
          
          <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-block">
              <input type="radio" name="status" value="1" title="启用" {if $info.pl_status==1}checked=""{/if}>
              <input type="radio" name="status" value="2" title="禁用" {if $info.pl_status==2}checked=""{/if}>
            </div>
          </div>
          

		  
         <div class="layui-form-item layui-layout-admin">
              <div class="layui-footer" style="left: 0;">
                <div class="layui-btn sub">立即提交</div>
                <button type="reset" class="layui-btn layui-btn-primary ">重置</button>
              </div>
          </div>
        </form>
      </div>
    </div>
  </div>
{include file="common_footer" /} 
<script>
layui.use(['upload', 'element', 'layer'], function(){
  var $ = layui.jquery
  ,upload = layui.upload
  ,element = layui.element
  ,layer = layui.layer;
  
  //常规使用 - 普通图片上传
  var uploadInst = upload.render({
    elem: '#test1'
    ,url: '{:url('upload/uploadpayico')}' //改成您自己的上传接口
    ,before: function(obj){
      //预读本地文件示例，不支持ie8
      //obj.preview(function(index, file, result){
      //  $('#demo1').attr('src', result); //图片链接（base64）
      //});
    }
    ,done: function(res){
      //如果上传失败
      if(res.success == "error"){
        return layer.msg('上传失败');
      }else{
		$("#ico").val(res.fileurl);
		$("#demo1").attr("src",res.fileurl);
		return layer.msg('上传完毕', {icon: 1});
	  }
	  
      //上传成功的一些操作
      //……
      $('#demoText').html(''); //置空上传失败的状态
    }
    ,error: function(){
      //演示失败状态，并实现重传
      var demoText = $('#demoText');
      demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-xs demo-reload">重试</a>');
      demoText.find('.demo-reload').on('click', function(){
        uploadInst.upload();
      });
    }
  });
  

});
</script>
<script>
$(".sub").click(function(){
	//if(!$(".btn").hasClass("sub")){return false;}
	var title   = $("#title").val();
	var ico     = $("#ico").val();
	var url     = $("#url").val();
	var content = $("#content").val();
	var code    = $("#code").val();
	var actname = $("#actname").val();
	var status  = $("input[name='status']:checked").val();

	
	if(title==""){
		show_error("支付名称不能为空!");
		return false;
	}
	
	if(code==""){
		show_error("支付编码不能为空!");
		return false;
	}
	
	if(actname==""){
		show_error("参数说明不能为空!");
		return false;
	}
	
	
	$.ajax({
		type:"POST",
		url:"{:url('paylist/edit')}",
		dataType:"json",
		data:{
			id:"{$info.pl_id}",
			title:title,
			ico:ico,
			url:url,
			content:content,
			status:status,
			code:code,
			actname:actname,
		},
		success:function(res){
			if(res.status == 1){
				window.parent.layer.closeAll();//关闭弹窗
				window.parent.location.reload();
				//show_toast_callurl(res.data,"{:url('device/index')}","success");
			}else{
				show_error(res.msg);
			}
		},
		error:function(jqXHR){
			console.log("Error: "+jqXHR.status);
		},
	});
	
});
</script>
</body>
</html>
