<?php
namespace app\common\service;

use think\Log;
use think\Db;

/**
 * 性能监控服务类
 * 提供性能监控、慢查询检测等功能
 */
class PerformanceService
{
    // 慢查询阈值（毫秒）
    const SLOW_QUERY_THRESHOLD = 1000;
    
    // 内存使用阈值（MB）
    const MEMORY_THRESHOLD = 128;
    
    /**
     * 开始性能监控
     * @param string $tag 监控标签
     * @return array 监控数据
     */
    public static function start($tag = 'default')
    {
        $data = [
            'tag' => $tag,
            'start_time' => microtime(true),
            'start_memory' => memory_get_usage(true),
            'start_peak_memory' => memory_get_peak_usage(true)
        ];
        
        return $data;
    }
    
    /**
     * 结束性能监控
     * @param array $startData 开始监控时的数据
     * @return array 性能统计结果
     */
    public static function end($startData)
    {
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        $endPeakMemory = memory_get_peak_usage(true);
        
        $result = [
            'tag' => $startData['tag'],
            'execution_time' => round(($endTime - $startData['start_time']) * 1000, 2), // 毫秒
            'memory_usage' => round(($endMemory - $startData['start_memory']) / 1024 / 1024, 2), // MB
            'peak_memory' => round($endPeakMemory / 1024 / 1024, 2), // MB
            'memory_delta' => round(($endPeakMemory - $startData['start_peak_memory']) / 1024 / 1024, 2) // MB
        ];
        
        // 记录慢操作
        if ($result['execution_time'] > self::SLOW_QUERY_THRESHOLD) {
            self::logSlowOperation($result);
        }
        
        // 记录高内存使用
        if ($result['peak_memory'] > self::MEMORY_THRESHOLD) {
            self::logHighMemoryUsage($result);
        }
        
        return $result;
    }
    
    /**
     * 监控数据库查询性能
     * @param string $sql SQL语句
     * @param array $params 参数
     * @param callable $callback 查询回调
     * @return mixed 查询结果
     */
    public static function monitorQuery($sql, $params, $callback)
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);
        
        try {
            $result = call_user_func($callback);
            
            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2);
            $memoryUsage = round((memory_get_usage(true) - $startMemory) / 1024 / 1024, 2);
            
            // 记录查询性能
            $queryData = [
                'sql' => $sql,
                'params' => $params,
                'execution_time' => $executionTime,
                'memory_usage' => $memoryUsage,
                'timestamp' => date('Y-m-d H:i:s')
            ];
            
            // 记录慢查询
            if ($executionTime > self::SLOW_QUERY_THRESHOLD) {
                self::logSlowQuery($queryData);
            }
            
            // 在调试模式下记录所有查询
            if (config('app_debug')) {
                Log::record('Query Performance: ' . json_encode($queryData, JSON_UNESCAPED_UNICODE), 'sql');
            }
            
            return $result;
            
        } catch (\Exception $e) {
            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2);
            
            // 记录查询错误
            Log::record("Query Error: {$sql} - {$e->getMessage()} (Time: {$executionTime}ms)", 'error');
            throw $e;
        }
    }
    
    /**
     * 记录慢查询
     * @param array $queryData 查询数据
     */
    private static function logSlowQuery($queryData)
    {
        try {
            $logData = [
                'sq_sql' => $queryData['sql'],
                'sq_params' => json_encode($queryData['params'], JSON_UNESCAPED_UNICODE),
                'sq_execution_time' => $queryData['execution_time'],
                'sq_memory_usage' => $queryData['memory_usage'],
                'sq_addtime' => $queryData['timestamp']
            ];
            
            // 检查慢查询表是否存在
            $tableExists = Db::query("SHOW TABLES LIKE 'web_slow_queries'");
            if (empty($tableExists)) {
                self::createSlowQueryTable();
            }
            
            Db::name('slow_queries')->insert($logData);
            
        } catch (\Exception $e) {
            Log::record('Log slow query failed: ' . $e->getMessage(), 'error');
        }
    }
    
    /**
     * 记录慢操作
     * @param array $operationData 操作数据
     */
    private static function logSlowOperation($operationData)
    {
        Log::record("Slow Operation: {$operationData['tag']} - {$operationData['execution_time']}ms", 'warning');
    }
    
    /**
     * 记录高内存使用
     * @param array $memoryData 内存数据
     */
    private static function logHighMemoryUsage($memoryData)
    {
        Log::record("High Memory Usage: {$memoryData['tag']} - {$memoryData['peak_memory']}MB", 'warning');
    }
    
    /**
     * 创建慢查询表
     */
    private static function createSlowQueryTable()
    {
        $sql = "
        CREATE TABLE IF NOT EXISTS `web_slow_queries` (
            `sq_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '慢查询ID',
            `sq_sql` text NOT NULL COMMENT 'SQL语句',
            `sq_params` text COMMENT '查询参数',
            `sq_execution_time` decimal(10,2) NOT NULL COMMENT '执行时间(毫秒)',
            `sq_memory_usage` decimal(10,2) DEFAULT 0.00 COMMENT '内存使用(MB)',
            `sq_addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间',
            PRIMARY KEY (`sq_id`),
            KEY `idx_execution_time` (`sq_execution_time`),
            KEY `idx_addtime` (`sq_addtime`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='慢查询记录表';
        ";
        
        Db::execute($sql);
    }
    
    /**
     * 获取系统性能统计
     * @return array 性能统计数据
     */
    public static function getSystemStats()
    {
        try {
            $stats = [
                'php_version' => PHP_VERSION,
                'memory_limit' => ini_get('memory_limit'),
                'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 2) . 'MB',
                'peak_memory' => round(memory_get_peak_usage(true) / 1024 / 1024, 2) . 'MB',
                'execution_time' => round(microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'], 4) . 's',
                'included_files' => count(get_included_files()),
                'server_load' => function_exists('sys_getloadavg') ? sys_getloadavg() : 'N/A'
            ];
            
            // 数据库连接统计
            try {
                $dbStats = Db::getConfig();
                $stats['database'] = [
                    'type' => $dbStats['type'] ?? 'unknown',
                    'hostname' => $dbStats['hostname'] ?? 'unknown',
                    'database' => $dbStats['database'] ?? 'unknown'
                ];
            } catch (\Exception $e) {
                $stats['database'] = 'Error: ' . $e->getMessage();
            }
            
            return $stats;
            
        } catch (\Exception $e) {
            Log::record('Get system stats failed: ' . $e->getMessage(), 'error');
            return [];
        }
    }
    
    /**
     * 获取慢查询统计
     * @param int $days 统计天数
     * @return array 慢查询统计
     */
    public static function getSlowQueryStats($days = 7)
    {
        try {
            $tableExists = Db::query("SHOW TABLES LIKE 'web_slow_queries'");
            if (empty($tableExists)) {
                return [];
            }
            
            $startDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
            
            $stats = [
                'total_count' => Db::name('slow_queries')
                    ->where('sq_addtime', '>=', $startDate)
                    ->count(),
                    
                'avg_execution_time' => Db::name('slow_queries')
                    ->where('sq_addtime', '>=', $startDate)
                    ->avg('sq_execution_time'),
                    
                'max_execution_time' => Db::name('slow_queries')
                    ->where('sq_addtime', '>=', $startDate)
                    ->max('sq_execution_time'),
                    
                'top_slow_queries' => Db::name('slow_queries')
                    ->where('sq_addtime', '>=', $startDate)
                    ->order('sq_execution_time desc')
                    ->limit(10)
                    ->select()
            ];
            
            return $stats;
            
        } catch (\Exception $e) {
            Log::record('Get slow query stats failed: ' . $e->getMessage(), 'error');
            return [];
        }
    }
    
    /**
     * 清理过期的性能日志
     * @param int $days 保留天数
     */
    public static function cleanOldLogs($days = 30)
    {
        try {
            $expireDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
            
            // 清理慢查询记录
            $tableExists = Db::query("SHOW TABLES LIKE 'web_slow_queries'");
            if (!empty($tableExists)) {
                $deleted = Db::name('slow_queries')
                    ->where('sq_addtime', '<', $expireDate)
                    ->delete();
                    
                Log::record("Cleaned {$deleted} old slow query records", 'info');
            }
            
        } catch (\Exception $e) {
            Log::record('Clean old performance logs failed: ' . $e->getMessage(), 'error');
        }
    }
}
