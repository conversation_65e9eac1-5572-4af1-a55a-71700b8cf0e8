<?php
namespace app\common\service;

use think\Cache;
use think\Db;
use think\Log;

/**
 * 缓存服务类
 * 提供查询缓存、数据缓存等功能
 */
class CacheService
{
    // 缓存前缀
    const PREFIX = 'app_cache_';
    
    // 默认缓存时间（秒）
    const DEFAULT_EXPIRE = 3600;
    
    /**
     * 获取缓存
     * @param string $key 缓存键
     * @param mixed $default 默认值
     * @return mixed 缓存值
     */
    public static function get($key, $default = null)
    {
        try {
            $cacheKey = self::PREFIX . $key;
            return Cache::get($cacheKey, $default);
        } catch (\Exception $e) {
            Log::record('Cache get failed: ' . $e->getMessage(), 'error');
            return $default;
        }
    }
    
    /**
     * 设置缓存
     * @param string $key 缓存键
     * @param mixed $value 缓存值
     * @param int $expire 过期时间（秒）
     * @return bool 是否成功
     */
    public static function set($key, $value, $expire = self::DEFAULT_EXPIRE)
    {
        try {
            $cacheKey = self::PREFIX . $key;
            return Cache::set($cacheKey, $value, $expire);
        } catch (\Exception $e) {
            Log::record('Cache set failed: ' . $e->getMessage(), 'error');
            return false;
        }
    }
    
    /**
     * 删除缓存
     * @param string $key 缓存键
     * @return bool 是否成功
     */
    public static function delete($key)
    {
        try {
            $cacheKey = self::PREFIX . $key;
            return Cache::rm($cacheKey);
        } catch (\Exception $e) {
            Log::record('Cache delete failed: ' . $e->getMessage(), 'error');
            return false;
        }
    }
    
    /**
     * 清空缓存
     * @param string $tag 缓存标签
     * @return bool 是否成功
     */
    public static function clear($tag = null)
    {
        try {
            if ($tag) {
                return Cache::clear($tag);
            } else {
                return Cache::clear();
            }
        } catch (\Exception $e) {
            Log::record('Cache clear failed: ' . $e->getMessage(), 'error');
            return false;
        }
    }
    
    /**
     * 缓存查询结果
     * @param string $key 缓存键
     * @param callable $callback 查询回调函数
     * @param int $expire 过期时间（秒）
     * @return mixed 查询结果
     */
    public static function remember($key, $callback, $expire = self::DEFAULT_EXPIRE)
    {
        $data = self::get($key);
        
        if ($data === null) {
            $data = call_user_func($callback);
            if ($data !== null) {
                self::set($key, $data, $expire);
            }
        }
        
        return $data;
    }
    
    /**
     * 缓存用户信息
     * @param int $userId 用户ID
     * @param string $userType 用户类型 (admin/substation/distribution)
     * @return array|null 用户信息
     */
    public static function getUserInfo($userId, $userType = 'admin')
    {
        $key = "user_info_{$userType}_{$userId}";
        
        return self::remember($key, function() use ($userId, $userType) {
            switch ($userType) {
                case 'substation':
                    return Db::name('substation')->where('su_id', $userId)->find();
                case 'distribution':
                    return Db::name('distribution')->where('di_id', $userId)->find();
                case 'admin':
                default:
                    return Db::name('users')->where('u_id', $userId)->find();
            }
        }, 1800); // 30分钟缓存
    }
    
    /**
     * 清除用户缓存
     * @param int $userId 用户ID
     * @param string $userType 用户类型
     */
    public static function clearUserCache($userId, $userType = 'admin')
    {
        $key = "user_info_{$userType}_{$userId}";
        self::delete($key);
    }
    
    /**
     * 缓存系统配置
     * @return array 系统配置
     */
    public static function getSystemConfig()
    {
        $key = 'system_config';
        
        return self::remember($key, function() {
            return Db::name('setup')->find();
        }, 7200); // 2小时缓存
    }
    
    /**
     * 清除系统配置缓存
     */
    public static function clearSystemConfig()
    {
        self::delete('system_config');
    }
    
    /**
     * 缓存权限规则
     * @param int $groupId 用户组ID
     * @return array 权限规则
     */
    public static function getAuthRules($groupId)
    {
        $key = "auth_rules_{$groupId}";
        
        return self::remember($key, function() use ($groupId) {
            $group = Db::name('auth_group')->where('id', $groupId)->find();
            if (!$group || !$group['rules']) {
                return [];
            }
            
            $ruleIds = explode(',', $group['rules']);
            return Db::name('auth_rule')
                ->where('id', 'in', $ruleIds)
                ->where('status', 1)
                ->order('sort asc')
                ->select();
        }, 3600); // 1小时缓存
    }
    
    /**
     * 清除权限缓存
     * @param int $groupId 用户组ID
     */
    public static function clearAuthCache($groupId = null)
    {
        if ($groupId) {
            self::delete("auth_rules_{$groupId}");
        } else {
            // 清除所有权限缓存
            $groups = Db::name('auth_group')->column('id');
            foreach ($groups as $id) {
                self::delete("auth_rules_{$id}");
            }
        }
    }
    
    /**
     * 缓存统计数据
     * @param string $type 统计类型
     * @param array $params 参数
     * @return mixed 统计结果
     */
    public static function getStatistics($type, $params = [])
    {
        $key = "statistics_{$type}_" . md5(serialize($params));
        
        return self::remember($key, function() use ($type, $params) {
            switch ($type) {
                case 'substation_count':
                    return Db::name('substation')->where('su_status', 1)->count();
                    
                case 'distribution_count':
                    $where = ['di_status' => 1];
                    if (isset($params['su_id'])) {
                        $where['di_su_id'] = $params['su_id'];
                    }
                    return Db::name('distribution')->where($where)->count();
                    
                case 'today_money':
                    $where = ['bl_status' => 2];
                    if (isset($params['su_id'])) {
                        $where['su_id'] = $params['su_id'];
                    }
                    return Db::name('bill')
                        ->where($where)
                        ->whereTime('bl_addtime', 'today')
                        ->sum('bl_money');
                        
                default:
                    return null;
            }
        }, 300); // 5分钟缓存
    }
    
    /**
     * 清除统计缓存
     * @param string $type 统计类型
     */
    public static function clearStatistics($type = null)
    {
        if ($type) {
            // 清除特定类型的统计缓存（需要遍历所有可能的参数组合）
            $pattern = "statistics_{$type}_*";
            // 这里简化处理，实际应用中可能需要更复杂的缓存键管理
            self::clear();
        } else {
            // 清除所有统计缓存
            self::clear();
        }
    }
    
    /**
     * 获取缓存统计信息
     * @return array 缓存统计
     */
    public static function getCacheStats()
    {
        try {
            // 这里的实现取决于具体的缓存驱动
            // 以下是一个示例实现
            return [
                'hit_rate' => '95%',
                'memory_usage' => '128MB',
                'keys_count' => 1500,
                'uptime' => '7 days'
            ];
        } catch (\Exception $e) {
            Log::record('Get cache stats failed: ' . $e->getMessage(), 'error');
            return [];
        }
    }
    
    /**
     * 预热缓存
     * 在系统启动或低峰期预先加载常用数据到缓存
     */
    public static function warmUp()
    {
        try {
            // 预热系统配置
            self::getSystemConfig();
            
            // 预热用户组权限
            $groups = Db::name('auth_group')->where('status', 1)->column('id');
            foreach ($groups as $groupId) {
                self::getAuthRules($groupId);
            }
            
            // 预热统计数据
            self::getStatistics('substation_count');
            self::getStatistics('distribution_count');
            
            Log::record('Cache warm up completed', 'info');
            
        } catch (\Exception $e) {
            Log::record('Cache warm up failed: ' . $e->getMessage(), 'error');
        }
    }
}
