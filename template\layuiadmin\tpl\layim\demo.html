<div class="layui-btn-container LAY-senior-im-chat-demo">
  <button class="layui-btn" data-type="chat">自定义会话</button>
  <button class="layui-btn" data-type="message">接受好友的消息</button>
  <button class="layui-btn" data-type="messageAudio">接受音频消息</button>
  <button class="layui-btn" data-type="messageVideo">接受视频消息</button>
  <button class="layui-btn" data-type="messageTemp">接受临时会话消息</button>
  
  <br>
  
  <button class="layui-btn" data-type="add">申请好友</button>
  <button class="layui-btn" data-type="addqun">申请加群</button>
  <button class="layui-btn" data-type="addFriend">同意好友</button>
  <button class="layui-btn" data-type="addGroup">增加群组到主面板</button>
  <button class="layui-btn" data-type="removeFriend">删除主面板好友</button>
  <button class="layui-btn" data-type="removeGroup">删除主面板群组</button>
  
  <br>
  <button class="layui-btn" data-type="setGray">置灰离线好友</button>
  <button class="layui-btn" data-type="unGray">取消好友置灰</button>
  
  <button class="layui-btn" data-type="kefu1">在线客服一</button>
  <button class="layui-btn" data-type="kefu2">在线客服二</button>
  
  <button class="layui-btn" data-type="mobile">移动端版本</button>
</div>