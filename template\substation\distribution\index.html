<!DOCTYPE html>
<html>
<head>
	{include file="common_header" /}
	{include file="common_top" /}
</head>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-body">
          	<div class="layui-box">
			<button class="layui-btn layuiadmin-btn-tags" data-type="add">添加</button>
			{if !empty($webinfo.text3)}<a class="layui-btn layuiadmin-btn-tags" href="{$webinfo.text3}" target="_blank">功能说明</a>{/if}
		    </div>
			<div class="layui-form" lay-filter="component-form-element">
            <table class="layui-table" lay-even="" lay-skin="nob">
            <thead>
              <tr>
                <th width="35" rowspan="2">序号</th>
                <th rowspan="2">状态</th>
				<th rowspan="2">用户名（实名）</th>
                <th rowspan="2">所属群组</th>
                <th rowspan="2">分销比例</th>
                <th rowspan="2">手机号</th>
                <th rowspan="2">支付宝</th>
                <th rowspan="2">微信</th>
                <th rowspan="2">余额</th>
                <th colspan="6" style="text-align:center">数据统计</th>
                <th width="120" rowspan="2">管理</th>
              </tr>
              <tr>
                <th style="text-align:center">群数</th>
                <th style="text-align:center">总收</th>
                <th style="text-align:center">本月</th>
                <th style="text-align:center">收益</th>
                <th style="text-align:center">利润</th>
                <th style="text-align:center">今日收款</th>
              </tr> 
            </thead>
            <tbody>
             {volist name="list" id="vo"}
				<tr id="tr_{$vo.du_id}">
					<td class="text-center">{$vo.du_id}</td>
                    <td>{if $vo.du_status==1}<span class="layui-badge-dot layui-bg-green"></span>{else}<span class="layui-badge-dot"></span>{/if}</td>
                    <td><span class="layui-badge-rim">{$vo.du_name}({$vo.du_smname})</span></td>
                    <td>{$vo.dg_title}</td>
                    <td><span class="layui-badge-rim">{$vo.dg_count} %</span></td>
                    <td>{$vo.du_phone}</td>
                    <td>{$vo.du_zfb}</td>
                    <td>{$vo.du_wx}</td>
                    <td>{$vo.du_money}</td>
                    <td style="text-align:center"><span class="layui-badge layui-bg-cyan">{$vo.count_group}</span></td>
                    <td style="text-align:center"><span class="layui-badge">{$vo.count_z_money|round=###,2} 元</span></td>
                    <td style="text-align:center"><span class="layui-badge">{$vo.count_cs|round=###,2} 元</span></td>
                    <td style="text-align:center"><span class="layui-badge">{$vo.count_s_money|round=###,2} 元</span></td>
                    <td style="text-align:center"><span class="layui-badge">{$vo.count_p_money|round=###,2} 元</span></td>
                    <td style="text-align:center"><span class="layui-badge">{$vo.count_daymoney|round=###,2} 元</span></td>
					<td>
							<div class="layui-btn-group">
								<button class="layui-btn layui-btn-sm" onClick="calldel('{:url('distribution/del',array('id'=>$vo.du_id))}','tr_{$vo.du_id}')"><i class="layui-icon">&#xe640;</i></button>
								<button class="layui-btn layui-btn-sm" data-type="edit" data-id="{$vo.du_id}"><i class="layui-icon">&#xe642;</i></button>
								<button class="layui-btn layui-btn-sm" data-type="login" data-user="{$vo.du_name}" data-pass="{$vo.du_pass}">登</button>
							</div>
					</td>
				</tr>
			{/volist}
            </tbody>
          </table> 
		  </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  {include file="common_footer" /} 
   <script>
	layui.use(['index','form'], function(){
		
		var form = layui.form;
		


    var $ = layui.$, active = {
   
   
	  login: function(){
		  var user = $(this).data('user');
		  var pass = $(this).data('pass');
		  var url = '{:url('/fenxiao.php/index/login_admin',array('username'=>'AAAAAA','password'=>'BBBBBB'))}';
		 
		  url = url.replace("/substation.php","");
		  url = url.replace("AAAAAA",user);
		  url = url.replace("BBBBBB",pass)
		  window.open(url,'_blank');
      },
   
	 
	  add: function(){
        layer.open({
          type: 2
          ,title: '添加分销信息'
          ,content: '{:url('distribution/add')}'
          ,area: ['650px', '710px']
        }); 
      },
	 
	 
	  edit: function(){
		var id = $(this).data('id');
		var url = '{:url('distribution/edit',array('id'=>'AAAAAA'))}';
		url = url.replace("AAAAAA",id)
        layer.open({
          type: 2
          ,title: '修改分销信息'
          ,content: url
          ,area: ['650px', '710px']
        }); 
      },
	 
	  

    } 
	
	 
    $('.layui-btn').on('click', function(){
      var type = $(this).data('type');
      active[type] ? active[type].call(this) : '';
    });

	
  });
  </script>
</body>
</html>