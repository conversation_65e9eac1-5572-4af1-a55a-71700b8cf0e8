<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8"/>
		<meta name="viewport" content="target-densitydpi=device-dpi, width=device-width,height=device-height, initial-scale=1, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
		<meta name="format-detection" content="telephone=no" />
		<title>{if $hadxxx}定位中...{else /}{$info.wxg_title}{/if}</title>
		<link type="text/css" rel="stylesheet" href="/template/group/index/css.css"/>
		<script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
        <script src="/template/group/index/layer/mobile/layer.js"></script>
        <script>
            window._AMapSecurityConfig = {
                securityJsCode:'61f55187b6feeb3794ccbe8fba390442',
            }
        </script>
        <script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=fdc6f731641c83601f4ffeb3dca10cd6"></script>
	</head>
	<body>
	    <script>
            var titles = "{$info.wxg_title}";
            var titles_fix="{$info.wxg_title_fix}";
            var titles_last=titles_fix;
            
            var loading=layer.open({type:3,shadeClose:false,content:'定位中...'})
            
            AMap.plugin(['AMap.Geolocation','AMap.Geocoder'], function () {
                var geolocation = new AMap.Geolocation({
                    enableHighAccuracy: true,//是否使用高精度定位，默认:true
                    timeout: 4000,          //超过10秒后停止定位，默认：无穷大
                    maximumAge: 0,           //定位结果缓存0毫秒，默认：0
                });
                
                getpos(geolocation);
                
                function getpos(geolocation){
                    geolocation.getCurrentPosition(function(status,result){
                        if(status=='complete'){
                            let geocoder = new AMap.Geocoder()

                            let lnglat = result.position
                            geocoder.getAddress(lnglat,function(status,result){
                                if(status=='complete'){
                                    var city=result.regeocode.addressComponent.city
                                    city=city.replace('市','')
                                    titles=titles.replace('xxx',city);
                                    titles_last=titles
                                    //$("#quntit").html(titles);
                                    $("title").html(titles);
                                }else{
                                    //$("#quntit").html(titles_fix);
                                    $("title").html(titles_fix);
                                }
                                
                                layer.close(loading)
                            })
                        }else{
                            //$("#quntit").html(titles_fix);
                            $("title").html(titles_fix);
                            layer.close(loading)
                        }
                    });
                }
            });
        </script>
<style>
.idjshow{width:94%; height:30px; background:#000;opacity:0.8; border-radius: 15px; line-height:30px; color:#FFF; text-align:center; margin-left:auto; margin-right:auto; z-index:99999; position:fixed; top:20px; left:3%}
#qunbtn{
    border:0;
    margin:0 auto;
    left:27%;
    width: 46%;
    background: #05C160;
    font-size: 17px;
    padding:13px;
    bottom:0.8rem;
    font-weight: bold;
}
</style>
<div id="idjshow"></div>



	<div style="width:100%;">
	    <img src="{$info.wxg_img}" style="width:100%;">
	</div>

	<button class="qunbtn" onClick="btnfun()" id="qunbtn">{$info.wxg_buttitle}</button>
	<div id="tzurlcontent"></div>
		<div class="xuanfu">
			<a href="{:url('index/kefu',array('id'=>$info.wxg_id))}"><img class="kefu2" src="/template/group/index/images/service.gif"/></a>
		</div>
		
<script>
 var names = ["李1","李2","李3","李4","李5"];
 var names_count = names.length;
 var names_i = 0;

 setInterval(function(){
	
	if($("#idjjshow").is(':visible')){
		$("#idjshow").html("");
	}else{
		if(names_i == names_count-1){
			names_i = 0;
		}else{
			names_i = names_i + 1;
		}
		
		tmp_name = names[names_i];
		tmp_str  = '<div class="idjshow" id="idjjshow">'+tmp_name+'*** 刚刚支付了{$info.wxg_money}元</div>';
		$("#idjshow").html(tmp_str);
		
	}
	console.log(tmp_str);
 },1300); 
 
</script>	
		
    <script>
        
        var qunbtn = "{$info.wxg_buttitle}";
        qunbtn = qunbtn.replace("[加粗]","<strong>")
        qunbtn = qunbtn.replace("[/加粗]","</strong>")
        qunbtn = qunbtn.replace("[加大+1]","<font size='+1'>")
        qunbtn = qunbtn.replace("[加大+2]","<font size='+2'>")
        qunbtn = qunbtn.replace("[加大+3]","<font size='+3'>")
        qunbtn = qunbtn.replace("[加大+4]","<font size='+4'>")
        qunbtn = qunbtn.replace("[加大+5]","<font size='+5'>")
        qunbtn = qunbtn.replace("[/加大]","</font>")
        $("#qunbtn").html(qunbtn);
        
        
        /*var titles = "{$info.wxg_title}";
        var citycode =returnCitySN.cname;
        titles = titles.replace("【本地】",citycode)
        $("#quntit").html(titles);
        $("title").html(titles);*/
    </script>
	<script>
	function btnfun(){
		$(".qunbtn").attr('disabled','disabled');
		$.ajax({
			type:"POST",
			url:"{:url('index/paylist')}",
			dataType:"json",
			data:{
				id:{$id},
				title:titles_last
			},
			success:function(res){
				if(res.status == 1){
					window.location.href = res.msg;
				}else if(res.status == 2){
				    $("#tzurlcontent").html(res.msg);
				    //alert();
				}else{
					show_error(res.msg);
				}
			},
			error:function(jqXHR){
				console.log("Error: "+jqXHR.status);
			},
		});
	}						
	</script>
	</body>
</html>