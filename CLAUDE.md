# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于 ThinkPHP 5.0 框架的多站点分销管理系统，采用 MVC 架构模式，支持分站管理、分销系统、用户权限控制等功能。

## 核心架构

### 框架结构
- **ThinkPHP 5.0**: 采用命名空间和 PSR-4 自动加载规范
- **入口文件**: `index.php` - 应用入口，定义了基础路径和配置
- **应用目录**: `application/` - 包含所有业务模块
- **配置目录**: `config/` - 全局配置和模块特定配置
- **模板目录**: `template/` - 前端模板文件
- **静态资源**: `template/layuiadmin/` - LayUI 前端框架
- **扩展目录**: `extend/` - 第三方扩展类库

### 业务模块详细结构
- **website**: 主站管理模块
  - 控制器: 用户管理、权限管理、分站管理、系统设置等
  - 核心功能: 超级管理员后台、权限分配、系统配置
- **substation**: 分站管理模块  
  - 控制器: 分站后台、分销管理、账单管理、提现管理
  - 核心功能: 分站独立管理、下级分销管控
- **fenxiao**: 分销管理模块
  - 控制器: 分销商后台、提现申请、账单查看
  - 核心功能: 分销商独立操作界面
- **group**: 群组管理模块 
- **notify/notifysc/notifyzfb**: 支付回调通知模块
  - 处理不同支付渠道的异步通知

### 控制器层架构
- **Base 控制器**: 各模块继承的基础控制器
  - `website/controller/Base.php`: 主站权限控制和会话管理
  - `substation/controller/Base.php`: 分站权限控制和会话管理
- **权限控制**:
  - 白名单 IP 检测 (`CheckWhiteIP()`)
  - 登录状态验证 (`CheckLoginStatus()`)
  - 路由权限过滤 (`$UrlNoPower`, `$ActionNoPower`)
- **核心控制器**:
  - `Index`: 登录、退出登录
  - `Users`: 用户管理 (CRUD)
  - `Substation`: 分站管理
  - `Distribution`: 分销管理
  - `Center`: 个人中心
  - `Upload`: 文件上传

### 模型层架构
- **基础模型**: `application/common/model/Base.php`
- **核心业务模型** (共26个):
  - `Users`: 用户登录、密码管理、状态管理
  - `Substation`: 分站登录、资金管理
  - `Distribution`: 分销登录、密码管理
  - `Bill`: 账单管理
  - `*tixian`: 各类提现管理
- **数据库配置**: `config/database.php` - MySQL 数据库配置
- **数据表前缀**: `web_` - 所有数据表使用此前缀

### 视图层架构
- **模板引擎**: ThinkPHP 5.0 内置模板引擎
- **前端框架**: LayUI - 响应式后台UI框架
- **模板组织**:
  - `template/{module}/` - 对应各业务模块的视图文件
  - `common_header.html` - 公共头部
  - `common_footer.html` - 公共底部
  - `common_top.html` - 公共导航
- **静态资源**:
  - `template/layuiadmin/` - LayUI 管理后台模板
  - `template/static/` - 自定义样式和脚本
  - `echarts.min.js` - 图表组件

### 配置层架构
- **全局配置**: `config/config.php`
- **模块配置**: `config/{module}/config.php`
- **扩展配置**: `config/extra/`
  - `web.php`: 系统基本信息配置
  - `ip.php`: IP 白名单配置
- **路由配置**: `config/route.php` (使用默认路由模式)

## 开发命令

### 环境要求
- PHP 5.4+ (推荐 PHP 7.x)
- MySQL 5.6+
- Web 服务器支持 URL 重写

### 本地开发
```bash
# 使用 PHP 内置服务器快速测试
cd public/
php -S localhost:8888 router.php
```

### 数据库初始化
```bash
# 导入完整数据库结构
mysql -u username -p database_name < database_complete_init.sql

# 应用优化脚本
mysql -u username -p database_name < database_optimization.sql
```

## 核心配置

### 应用配置 (`config/config.php`)
- 调试模式: `app_debug` = true (开发环境)
- 多模块支持: `app_multi_module` = true
- 默认时区: `default_timezone` = 'PRC'

### 数据库配置 (`config/database.php`)
- 类型: MySQL
- 字符集: UTF-8
- 表前缀: `web_`
- 调试模式: 开启

## 权限系统架构

项目采用基于角色的权限控制系统 (RBAC):

### 权限表结构
- **规则表** (`web_auth_rule`): 定义权限规则，存储控制器/方法路径
- **分组表** (`web_auth_group`): 权限分组，关联多个规则
- **关联表** (`web_auth_group_access`): 用户与权限分组的多对多关联

### 权限验证流程
1. **IP 白名单检测**: `CheckWhiteIP()` - 验证访问域名是否在允许列表
2. **登录状态检测**: `CheckLoginStatus()` - 验证用户登录状态  
3. **路由权限过滤**: 
   - `$UrlNoPower`: 无需登录的路由白名单
   - `$ActionNoPower`: 登录后无需权限验证的公共路由
4. **动态权限验证**: 使用 `Auth` 类进行细粒度权限检查

### 会话管理
- **主站**: `session("uid")`, `session("uphone")`, `session("unickname")`
- **分站**: `session("su_id")`, `session("su_g_id")`
- **分销**: 独立的分销商会话管理

## 核心业务逻辑

### 用户管理体系
- **三级用户架构**:
  - 超级管理员 (`u_supermanage` = 2)
  - 普通管理员 (`u_supermanage` = 1)
  - 分站管理员 (独立的分站账户体系)
- **认证方式**:
  - 主站: 手机号 + MD5密码
  - 分站: 用户名 + 明文密码
  - 分销: 用户名 + 明文密码
- **安全机制**:
  - 登录次数记录
  - 登录时间和IP追踪
  - 用户状态控制 (正常/禁用/过期)

### 分站管理体系
- **层级关系**: 主站 → 分站 → 分销商
- **分站特性**:
  - 独立域名绑定
  - 分组管理 (按套餐分组)
  - 到期时间控制
  - 资金账户管理 (`su_fz_money`)
- **上下级关系**: `su_s_id` 字段记录上级分站ID

### 分销管理体系  
- **分销层级**: 支持多级分销结构
- **资金流转**: 
  - 分销提成计算
  - 提现申请流程
  - 资金状态跟踪
- **业务绑定**: 分销商与具体分站关联 (`di_su_id`)

### 支付回调系统
- **多渠道支持**: 微信、支付宝、第三方支付
- **回调处理**: 
  - `notify.php` - 通用回调
  - `notifysc.php` - 特定渠道回调  
  - `notifyzfb.php` - 支付宝回调
- **安全验证**: 签名校验和订单状态同步

### 地理位置服务
- **百度地图API集成**: 使用 `ak=VHr3iKqzTkSq4fBgK7ITGAyQ8FFZR0Om`
- **IP定位功能**:
  - `getCity()`: 获取用户所在城市
  - `getClientIP()`: 获取真实客户端IP (支持代理环境)

## 数据库表结构概览

### 用户权限相关
- `web_users` - 主站用户表 (手机号登录)
- `web_users_logs` - 用户登录日志
- `web_auth_rule` - 权限规则定义
- `web_auth_group` - 权限分组
- `web_auth_group_access` - 用户权限分组关联

### 分站管理相关  
- `web_substation` - 分站基础信息
- `web_substationgroup` - 分站分组 (套餐)

### 分销管理相关
- `web_distribution` - 分销商信息
- `web_distributiongroup` - 分销分组
- `web_distributiontixian` - 分销提现记录

### 业务功能相关
- `web_bill` - 账单记录
- `web_dianka` - 点卡管理
- `web_diankalog` - 点卡使用日志
- `web_chouyong` - 抽佣记录
- `web_duizhang` - 对账记录

## 关键文件路径

### 核心入口与配置
- **应用入口**: `index.php`
- **全局配置**: `config/config.php`
- **数据库配置**: `config/database.php`
- **系统配置**: `config/extra/web.php`
- **IP白名单**: `config/extra/ip.php`

### 业务逻辑层
- **公共函数**: `application/common.php` - IP定位等工具函数
- **基础模型**: `application/common/model/Base.php`
- **用户模型**: `application/common/model/Users.php` - 用户认证核心
- **分站模型**: `application/common/model/Substation.php`
- **分销模型**: `application/common/model/Distribution.php`

### 控制器层
- **主站基础**: `application/website/controller/Base.php` - 权限控制核心
- **分站基础**: `application/substation/controller/Base.php`
- **登录控制**: `application/{module}/controller/Index.php`

### 视图层
- **主站模板**: `template/website/`
- **分站模板**: `template/substation/`
- **LayUI框架**: `template/layuiadmin/`
- **公共组件**: `template/{module}/common_*.html`

### 支付回调
- **通用回调**: `notify.php`
- **微信回调**: `notifysc.php`  
- **支付宝回调**: `notifyzfb.php`

## 开发注意事项

### 安全要求
1. **敏感信息管理**:
   - 数据库密码存储在 `config/database.php`
   - 百度地图API密钥硬编码在 `application/common.php`
   - 生产环境需要使用环境变量管理敏感信息

2. **权限控制**:
   - 所有控制器必须继承对应的 Base 控制器
   - 新增路由需要在 `$UrlNoPower` 或权限表中配置
   - IP白名单机制需要正确配置域名

3. **数据安全**:
   - 用户密码使用 MD5 加密 (建议升级为更安全的算法)
   - SQL查询需要使用ThinkPHP的查询构造器避免注入
   - 用户输入需要进行验证和过滤

### 部署要求
1. **服务器环境**:
   - PHP 5.4+ (推荐 PHP 7.x)
   - MySQL 5.6+
   - Apache/Nginx 支持 URL 重写

2. **目录权限**:
   - `runtime/` - 缓存和日志目录，需要可写权限
   - `upload/` - 文件上传目录，需要可写权限
   - `template/` - 模板目录，需要可读权限

3. **配置调整**:
   - 生产环境关闭 `app_debug` 模式
   - 生产环境关闭数据库 `debug` 模式
   - 配置正确的数据库连接信息
   - 设置合适的 `default_timezone`

### 开发建议
1. **代码规范**: 遵循 ThinkPHP 5.0 和 PSR-4 规范
2. **模块开发**: 新业务建议创建独立模块而非修改现有模块
3. **数据库**: 新表必须使用 `web_` 前缀保持一致性
4. **前端**: 使用 LayUI 框架保持界面风格一致性