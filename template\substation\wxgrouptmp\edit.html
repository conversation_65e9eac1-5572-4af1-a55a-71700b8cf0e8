<!DOCTYPE html>
<html>
<head>
	{include file="common_header" /}
	{include file="common_top" /}
	<script type="text/javascript" charset="utf-8" src="/ueditor/ueditor.config.js"></script>
    <script type="text/javascript" charset="utf-8" src="/ueditor/ueditor.all.min.js"> </script>
    <!--建议手动加在语言，避免在ie下有时因为加载语言失败导致编辑器加载失败-->
    <!--这里加载的语言文件会覆盖你在配置项目里添加的语言类型，比如你在配置项目里配置的是英文，这里加载的中文，那最后就是中文-->
    <script type="text/javascript" charset="utf-8" src="/ueditor/lang/zh-cn/zh-cn.js"></script>
</head>
<body>

  <div class="layui-fluid">
    <div class="layui-card">
      <div class="layui-card-body" style="padding: 15px;">
        <form class="layui-form" action="" lay-filter="component-form-group">
        
        
<div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
  <ul class="layui-tab-title">
    <li class="layui-this">基础设置</li>
    <li>内容设置</li>
    <li>展示广告</li>
  </ul>
  <div class="layui-tab-content" >
    <div class="layui-tab-item layui-show">  
        
        
        <div class="layui-form-item">
            <label class="layui-form-label">头像库选择</label>
            <div class="layui-input-block">
				<select name="headimg" id="headimg">
                    <option value="qq" {if $info.wxgt_headfile=="qq"}selected{/if}>扩列，交友，吃瓜</option>
                    <option value="za" {if $info.wxgt_headfile=="za"}selected{/if}>综合随机</option>
				</select>
            </div>
          </div> 
        
        
        
		<div class="layui-form-item">
            <label class="layui-form-label">群组头像</label>
			<input type="hidden" id="wxgt_headimg" name="wxgt_headimg" value="{$info.wxgt_img}">
            <div class="layui-input-block">
 <div class="layui-upload">
  <button type="button" class="layui-btn" id="test1">上传图片</button>
  <div class="layui-upload-list">
    <img class="layui-upload-img" src="{$info.wxgt_img}" id="demo1" style="width:60px; height:60px;">
    <p id="demoText"></p>
  </div>
</div>  
            </div>
          </div> 
          
          <div class="layui-form-item">
            <label class="layui-form-label">入群费用</label>
            <div class="layui-input-block">
              <input name="money" type="number"  class="layui-input" id="money" placeholder="入群费用" value="{$info.wxgt_money}">
            </div>
          </div> 
             
        
        <div class="layui-form-item">
            <label class="layui-form-label">群名称</label>
            <div class="layui-input-block">
              <input name="title" type="text"  class="layui-input" id="title" placeholder="群名称" value="{$info.wxgt_title}">
            </div>
          </div>  
          

          
          <div class="layui-form-item">
            <label class="layui-form-label">副标题</label>
            <div class="layui-input-block">
              <input name="subtitle" type="text"  class="layui-input" id="subtitle" placeholder="副标题" value="{$info.wxgt_subtitle}">
            </div>
          </div> 
          

          
          <div class="layui-form-item">
            <label class="layui-form-label">阅读数</label>
            <div class="layui-input-block">
              <input name="redcount" type="text"  class="layui-input" id="redcount" placeholder="阅读数" value="{$info.wxgt_redcount}">
            </div>
          </div>
          
          <div class="layui-form-item">
            <label class="layui-form-label">点赞数</label>
            <div class="layui-input-block">
              <input name="dzcount" type="number"  class="layui-input" id="dzcount" placeholder="点赞数" value="{$info.wxgt_dzcount}">
            </div>
          </div>
          
          <div class="layui-form-item">
            <label class="layui-form-label">想看数</label>
            <div class="layui-input-block">
              <input name="xxcount" type="number"  class="layui-input" id="xxcount" placeholder="点赞数" value="{$info.wxgt_xxcount}">
            </div>
          </div>
          
          
          <div class="layui-form-item">
            <label class="layui-form-label">按键名称</label>
            <div class="layui-input-block">
              <input name="buttitle" type="text"  class="layui-input" id="buttitle" placeholder="按键名称" value="{$info.wxgt_buttitle}">
            </div>
          </div>
          
          
          <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-block">
              <input type="radio" name="status" value="1" title="启用" {if $info.wxgt_status==1}checked=""{/if}>
              <input type="radio" name="status" value="2" title="禁用" {if $info.wxgt_status==2}checked=""{/if}>
            </div>
          </div>
          
          
          
 	</div>
    <div class="layui-tab-item">  
          
          
          
         <div class="layui-form-item">
            <label class="layui-form-label">区块一标题</label>
            <div class="layui-input-block">
              <input name="qjj_title" type="text"  class="layui-input" id="qjj_title" placeholder="区块一标题" value="{$info.wxgt_qjj_title}">
            </div>
          </div>
          
          <div class="layui-form-item">
            <label class="layui-form-label">区块一内容</label>
            <div class="layui-input-block">
              <input name="qjj_content" type="text"  class="layui-input" id="qjj_content" placeholder="区块一内容" value="{$info.wxgt_qjj_content}">
            </div>
          </div>
          
          
          
          
          <div class="layui-form-item">
            <label class="layui-form-label">区块二标题</label>
            <div class="layui-input-block">
              <input name="kuai_title" type="text"  class="layui-input" id="kuai_title" placeholder="区块二标题" value="{$info.wxgt_kuai_title}">
            </div>
          </div>
          
          <div class="layui-form-item">
            <label class="layui-form-label">区块二内容</label>
            <div class="layui-input-block">
              <textarea class="layui-input" name="kuai_content" id="kuai_content" style="height:180px;">{$info.wxgt_kuai_content}</textarea>
            </div>
          </div>       
          
          
          
          <div class="layui-form-item">
            <label class="layui-form-label">区块三标题</label>
            <div class="layui-input-block">
              <input name="kuai_title1" type="text"  class="layui-input" id="kuai_title1" placeholder="区块三标题" value="{$info.wxgt_kuai_title1}">
            </div>
          </div>
          
          
		<div class="layui-form-item">
            <label class="layui-form-label">区块三图片</label>

            <div class="layui-input-block">
	<script id="editor" type="text/plain" style="width:100%;height:250px;"></script>

            </div>
          </div>    
          
          
          
              
          <div class="layui-form-item">
            <label class="layui-form-label">群友评论</label>
            <div class="layui-input-block">
              <textarea class="layui-input" name="qunyou_content" id="qunyou_content" style="height:180px;">{$info.wxgt_qunyou_content}</textarea>
            </div>
          </div> 
          
          
 	</div>
    <div class="layui-tab-item">      
    


		<div class="layui-form-item">
            <label class="layui-form-label">客服二维码</label>
			<input type="hidden" id="wxgt_kefu" name="wxgt_kefu" value="{$info.wxgt_kefu}">
            <div class="layui-input-block">
 <div class="layui-upload">
  <button type="button" class="layui-btn" id="test3">上传图片</button>
  <div class="layui-upload-list">
    <img class="layui-upload-img" src="{$info.wxgt_kefu}" id="demo3" style="width:200px; height:200px;">
    <p id="demoText"></p>
  </div>
</div>  
            </div>
          </div> 


    
          
		<div class="layui-form-item">
            <label class="layui-form-label">广告二维码</label>
			<input type="hidden" id="wxgt_adurl" name="wxgt_adurl" value="{$info.wxgt_adurl}">
            <div class="layui-input-block">
 <div class="layui-upload">
  <button type="button" class="layui-btn" id="test2">上传图片</button>
  <div class="layui-upload-list">
    <img class="layui-upload-img" src="{$info.wxgt_adurl}" id="demo2" style="width:200px; height:200px;">
    <p id="demoText"></p>
  </div>
</div>  
            </div>
          </div> 
          
          
          
          
	</div>
  </div>
</div>           
             
          
         

	  
         <div class="layui-form-item layui-layout-admin">
              <div class="layui-footer" style="left: 0;">
                <div class="layui-btn sub">立即提交</div>
                <button type="reset" class="layui-btn layui-btn-primary ">重置</button>
              </div>
          </div>
        </form>
      </div>
    </div>
  </div>
{include file="common_footer" /} 
<script>

var ue = UE.getEditor('editor');
$(document).ready(function () {
    ue.ready(function () {
        ue.setContent('{$info.wxgt_kuai_imgs1}');
    });
});


layui.use(['upload', 'element', 'layer'], function(){
  var $ = layui.jquery
  ,upload = layui.upload
  ,element = layui.element
  ,layer = layui.layer;
  
  //常规使用 - 普通图片上传
  var uploadInst = upload.render({
    elem: '#test1'
    ,url: '{:url('upload/uploadface')}' //改成您自己的上传接口
    ,before: function(obj){
      //预读本地文件示例，不支持ie8
      //obj.preview(function(index, file, result){
      //  $('#demo1').attr('src', result); //图片链接（base64）
      //});
    }
    ,done: function(res){
      //如果上传失败
      if(res.success == "error"){
        return layer.msg('上传失败');
      }else{
		$("#wxgt_headimg").val(res.fileurl);
		$("#demo1").attr("src",res.fileurl);
		return layer.msg('上传完毕', {icon: 1});
	  }
	  
      //上传成功的一些操作
      //……
      $('#demoText').html(''); //置空上传失败的状态
    }
    ,error: function(){
      //演示失败状态，并实现重传
      var demoText = $('#demoText');
      demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-xs demo-reload">重试</a>');
      demoText.find('.demo-reload').on('click', function(){
        uploadInst.upload();
      });
    }
  });
  
  
  //常规使用 - 普通图片上传
  var uploadInst = upload.render({
    elem: '#test2'
    ,url: '{:url('upload/uploadface')}' //改成您自己的上传接口
    ,before: function(obj){
      //预读本地文件示例，不支持ie8
      //obj.preview(function(index, file, result){
      //  $('#demo1').attr('src', result); //图片链接（base64）
      //});
    }
    ,done: function(res){
      //如果上传失败
      if(res.success == "error"){
        return layer.msg('上传失败');
      }else{
		$("#wxgt_adurl").val(res.fileurl);
		$("#demo2").attr("src",res.fileurl);
		return layer.msg('上传完毕', {icon: 1});
	  }
	  
      //上传成功的一些操作
      //……
      $('#demoText').html(''); //置空上传失败的状态
    }
    ,error: function(){
      //演示失败状态，并实现重传
      var demoText = $('#demoText');
      demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-xs demo-reload">重试</a>');
      demoText.find('.demo-reload').on('click', function(){
        uploadInst.upload();
      });
    }
  }); 
  
  
  
  
  //常规使用 - 普通图片上传
  var uploadInst = upload.render({
    elem: '#test3'
    ,url: '{:url('upload/uploadface')}' //改成您自己的上传接口
    ,before: function(obj){
      //预读本地文件示例，不支持ie8
      //obj.preview(function(index, file, result){
      //  $('#demo1').attr('src', result); //图片链接（base64）
      //});
    }
    ,done: function(res){
      //如果上传失败
      if(res.success == "error"){
        return layer.msg('上传失败');
      }else{
		$("#wxgt_kefu").val(res.fileurl);
		$("#demo3").attr("src",res.fileurl);
		return layer.msg('上传完毕', {icon: 1});
	  }
	  
      //上传成功的一些操作
      //……
      $('#demoText').html(''); //置空上传失败的状态
    }
    ,error: function(){
      //演示失败状态，并实现重传
      var demoText = $('#demoText');
      demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-xs demo-reload">重试</a>');
      demoText.find('.demo-reload').on('click', function(){
        uploadInst.upload();
      });
    }
  }); 
  
  
  
  
});
</script>
<script>
$(".sub").click(function(){
	//if(!$(".btn").hasClass("sub")){return false;}
	var wxgt_headimg = $("#wxgt_headimg").val();
	var title       = $("#title").val();
	var subtitle    = $("#subtitle").val();
	var qjj_title   = $("#qjj_title").val();
	var qjj_content = $("#qjj_content").val();
	var redcount    = $("#redcount").val();
	var dzcount     = $("#dzcount").val();
	var xxcount     = $("#xxcount").val();
	var buttitle    = $("#buttitle").val();
	var money       = $("#money").val();
	
	var kuai_title     = $("#kuai_title").val();
	var kuai_content   = $("#kuai_content").val();
	var qunyou_content = $("#qunyou_content").val();
	var wxgt_adurl = $("#wxgt_adurl").val();
	var headimg = $("#headimg").val();
	var wxgt_kefu = $("#wxgt_kefu").val();
	var status      = $("input[name='status']:checked").val();
	
	var kuai_title1 = $("#kuai_title1").val();
	var kuai_imgs1 = UE.getEditor('editor').getContent();

	if(wxgt_headimg == ""){
		show_error("群组头像不能为空!");
		return false;
	}
	
	if(money == ""){
		show_error("入群费用不能为空!");
		return false;
	}

	if(title == ""){
		show_error("群名称不能为空!");
		return false;
	}
	
	if(subtitle == ""){
		show_error("副标题不能为空!");
		return false;
	}
	
	if(buttitle == ""){
		show_error("按键名称不能为空!");
		return false;
	}
	
	$.ajax({
		type:"POST",
		url:"{:url('wxgrouptmp/edit')}",
		dataType:"json",
		data:{
			id:{$info.wxgt_id},
			wxgt_headimg:wxgt_headimg,
			title:title,
			subtitle:subtitle,
			qjj_title:qjj_title,
			qjj_content:qjj_content,
			redcount:redcount,
			dzcount:dzcount,
			xxcount:xxcount,
			buttitle:buttitle,
			status:status,
			money:money,
			kuai_title:kuai_title,
			kuai_content:kuai_content,
			qunyou_content:qunyou_content,
			wxgt_adurl:wxgt_adurl,
			headimg:headimg,
			wxgt_kefu:wxgt_kefu,
			kuai_title1:kuai_title1,
	        kuai_imgs1:kuai_imgs1,
		},
		success:function(res){
			if(res.status == 1){
				window.parent.layer.closeAll();//关闭弹窗
				window.parent.location.reload();
				//show_toast_callurl(res.data,"{:url('device/index')}","success");
			}else{
				show_error(res.msg);
			}
		},
		error:function(jqXHR){
			console.log("Error: "+jqXHR.status);
		},
	});
	
});
</script>
</body>
</html>
