<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8"/>
		<meta name="viewport" content="target-densitydpi=device-dpi, width=device-width,height=device-height, initial-scale=1, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
		<meta name="format-detection" content="telephone=no" />
		<title>{if $hadxxx}定位中...{else /}{$info.wxg_title}{/if}</title>
		<link type="text/css" rel="stylesheet" href="/template/group/index/css.css"/>
		<script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
        
        <script src="/template/group/index/layer/mobile/layer.js"></script>
        <script>
            window._AMapSecurityConfig = {
                securityJsCode:'61f55187b6feeb3794ccbe8fba390442',
            }
        </script>
        <script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=fdc6f731641c83601f4ffeb3dca10cd6"></script>
        <style>
            .svvv table{width: 100%;}
            .svvv table img{width: 100%;}
        </style>
	</head>
	<body>
<style>
.idjshow{width:94%; height:30px; background:#000;opacity:0.8; border-radius: 15px; line-height:30px; color:#FFF; text-align:center; margin-left:auto; margin-right:auto; z-index:99999; position:fixed; top:20px; left:3%}
.qunbtn{
    border:0;
    margin:0 auto;
    left:27%;
    width: 46%;
    background: #05C160;
    font-size: 17px;
    padding:13px;
    bottom:0.8rem;
    font-weight: bold;
}
</style>
<div id="idjshow"></div>  
	    
		<div class="qun"><img src="{$info.wxg_img}"/><p class="quntit" id="quntit">{if $hadxxx}定位中...{else /}{$info.wxg_title}{/if}</p><p class="num">{$info.wxg_subtitle}</p></div>
        <script>
            var titles = "{$info.wxg_title}";
            var titles_fix="{$info.wxg_title_fix}";
            var titles_last=titles_fix;
            
            var loading=layer.open({type:3,shadeClose:false,content:'定位中...'})
            
            AMap.plugin(['AMap.Geolocation','AMap.Geocoder'], function () {
                var geolocation = new AMap.Geolocation({
                    enableHighAccuracy: true,//是否使用高精度定位，默认:true
                    timeout:4000,          //超过10秒后停止定位，默认：无穷大
                    maximumAge: 0,           //定位结果缓存0毫秒，默认：0
                });
                
                getpos(geolocation);
                
                function getpos(geolocation){
                    geolocation.getCurrentPosition(function(status,result){
                        if(status=='complete'){
                            let geocoder = new AMap.Geocoder()

                            let lnglat = result.position
                            geocoder.getAddress(lnglat,function(status,result){
                                if(status=='complete'){
                                    var city=result.regeocode.addressComponent.city
                                    city=city.replace('市','')
                                    titles=titles.replace('xxx',city);
                                    titles_last=titles
                                    $(".quntit").html(titles);
                                    $("title").html(titles);
                                }else{
                                    $(".quntit").html(titles_fix);
                                    $("title").html(titles_fix);
                                }
                                
                                layer.close(loading)
                            })
                        }else{
                            $(".quntit").html(titles_fix);
                            $("title").html(titles_fix);
                            layer.close(loading)
                        }
                    });
                }
            });
        </script>
		<div class="qunz">
		
		<div class="qunpeople">
			<div class="title2">群成员</div>
			{if false}
    			<div class="peoplez">
                	{volist name="qccontent" id="vo"}
                    <div class="peoplef"><img src="/face/{$info.wxg_headfile}/{$vo.headimg}"/><p>{$vo.nickname}</p></div>
                    {/volist}
    				<div class="peoplef">
    					<img src="/template/group/index/images/photoadd.jpg"/>
    					<p></p>
    				</div>
    
    			</div>
    		{else /}
    		    <div class="memimg">
                	<img src="{$info.wxg_memimg}" alt="" />
    			</div>
    			<style>
    			    .memimg img{
    			        width: 90%;
                        margin: 0 auto;
                        margin-top: 0.96rem;
                        display: block;
    			    }
    			</style>
			{/if}
		</div>
		

		
		<div class="qunstate"><div class="title">{$info.wxg_qjj_title}</div><div class="qs"><a href=""><p>{$info.wxg_qjj_content}</p></a></div></div>

		<div class="qunstate">
			<div class="title">{$info.wxg_kuai_title}</div>
            {volist name="content" id="vo"}
			<div class="question">
				<p class="ad">{$vo.title}</p>
				<p class="aw">{$vo.content}</p>
			</div>
            {/volist}
		</div>
		
		
		{if $info.wxg_kuai_title1 !="" and $info.wxg_kuai_imgs1 !=""}
		<div class="qunfuli">
			<div class="title">{$info.wxg_kuai_title1}</div>
			<div class="fuli svvv">
				{$info.wxg_kuai_imgs1}
			</div>
		</div>
		{/if}
		
		
		<div class="yuedu">阅读{$info.wxg_redcount}</div>
		<div class="qunicon">
		  <div class="qunicon1">
			<div class="icon1"><img src="/template/group/index/images/icon1.png"/><span>分享</span></div>
			<div class="icon1"><img src="/template/group/index/images/icon2.png"/><span>收藏</span></div>
		  </div>
		  <div class="qunicon1">
			<div class="icon1"><img src="/template/group/index/images/icon3.jpg"/><span>{$info.wxg_dzcount}</span></div>
			<div class="icon1"><img src="/template/group/index/images/icon4.jpg"/><span>{$info.wxg_xxcount}</span></div>
		  </div>
		</div>
		
	</div>
		<div class="qunliuyan">

			<div class="liuyantit">
				<p class="lyo">群友评论（精选）</p>
			</div>
			{volist name="qcontent" id="vo"}
			<div class="liuyanz">
				<img class="qleft" src="/face/{$info.wxg_headfile}/{$vo.headimg}"/>
				<div class="qcenter"><p class="nichen">{$vo.nickname}</p><div class="liuyan">{$vo.title}</div></div>
				<div class="qright"><img src="/template/group/index/images/icon3.png"/><p>赞：{$vo.count}</p></div>
			</div>
			{/volist}
		</div>
		
	<button class="qunbtn" id="qunbtn">加入群聊</button>
	<div id="tzurlcontent"></div>
		<div class="xuanfu">
			<a href="{:url('index/kefu',array('id'=>$info.wxg_id))}"><img class="kefu2" src="/template/group/index/images/service.gif"/></a>
		</div>
		<div class="step2">
		    <div class="qun">
		        <img src="{$info.wxg_img}"/>
		        <p class="quntit" id="quntit">{if $hadxxx}定位中...{else /}{$info.wxg_title}{/if}</p>
		        
		        <img src="/template/group/index/wxqun.jpg" style="width:21rem;height:auto; margin-top:1.7rem;"/>
		        <button  onClick="btnfun()" class="qunbtn">{php}echo str_replace('xxx',$info['wxg_money'],$info['wxg_buttitle']){/php}</button>
		    </div>
		    
		</div>
		
		<style>
		    .step2 .qunbtn{
		        z-index: 9999;
		    }
		    .step2 .qun{
		        background: #fff;
		        border-bottom: 0;
		    }
		    .step2{
		        position: fixed;
		        width: 100%;
		        height: 100%;
		        z-index: 99999;
		        background: #fff;
		        top:0;
		        left: 0;
		        display: none;
		    }
		</style>
<script>
 var names = ["李家闵","青狐","暧昧的","笑","黄六"];
 var names_count = names.length;
 var names_i = 0;

 setInterval(function(){
	
	if($("#idjjshow").is(':visible')){
		$("#idjshow").html("");
	}else{
		if(names_i == names_count-1){
			names_i = 0;
		}else{
			names_i = names_i + 1;
		}
		
		tmp_name = names[names_i];
		tmp_str  = '<div class="idjshow" id="idjjshow">'+tmp_name+'*** 刚刚支付了{$info.wxg_money}元入群</div>';
		$("#idjshow").html(tmp_str);
		
	}
	console.log(tmp_str);
 },1300); 
 
</script>
    <script>
    
    
    
    
    
    
    
    
    
        /*layer.open({
          title: '在线调试'
          ,content: '可以填写任意的layer代码'
        });*/
        var qunbtn = "立即加入群聊";
        qunbtn = qunbtn.replace("[加粗]","<strong>")
        qunbtn = qunbtn.replace("[/加粗]","</strong>")
        qunbtn = qunbtn.replace("[加大+1]","<font size='+1'>")
        qunbtn = qunbtn.replace("[加大+2]","<font size='+2'>")
        qunbtn = qunbtn.replace("[加大+3]","<font size='+3'>")
        qunbtn = qunbtn.replace("[加大+4]","<font size='+4'>")
        qunbtn = qunbtn.replace("[加大+5]","<font size='+5'>")
        qunbtn = qunbtn.replace("[/加大]","</font>")
        $("#qunbtn").html(qunbtn);
        
        
        var titles = "{$info.wxg_title}";
        /*var citycode =returnCitySN.cname;
        titles = titles.replace("【本地】",citycode)*/
        /*$("#quntit").html(titles);
        $("title").html(titles);*/
    </script>
	<script>
	$('#qunbtn').click(function(){
	    $('.step2').slideDown(100)
	});
	
	function btnfun(){
		$(".qunbtn").attr('disabled','disabled');
		$.ajax({
			type:"POST",
			url:"{:url('index/paylist')}",
			dataType:"json",
			data:{
				id:{$id},
				title:titles_last
			},
			success:function(res){
				if(res.status == 1){
					window.location.href = res.msg;
				}else if(res.status == 2){
				    $("#tzurlcontent").html(res.msg);
				    //alert();
				}else{
					show_error(res.msg);
				}
			},
			error:function(jqXHR){
				console.log("Error: "+jqXHR.status);
			},
		});
	}						
	</script>
	</body>
</html>