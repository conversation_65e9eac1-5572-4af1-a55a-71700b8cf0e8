<?php
namespace app\common\service;

use think\Request;
use think\Session;

/**
 * 安全服务类
 * 提供密码加密、CSRF保护、XSS防护等安全功能
 */
class SecurityService
{
    /**
     * 生成安全的密码哈希
     * @param string $password 原始密码
     * @param string $algorithm 加密算法 (bcrypt|argon2i|argon2id)
     * @return string 加密后的密码
     */
    public static function hashPassword($password, $algorithm = 'bcrypt')
    {
        switch ($algorithm) {
            case 'argon2i':
                if (defined('PASSWORD_ARGON2I')) {
                    return password_hash($password, PASSWORD_ARGON2I);
                }
                // 如果不支持Argon2i，回退到bcrypt
                return password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);
                
            case 'argon2id':
                if (defined('PASSWORD_ARGON2ID')) {
                    return password_hash($password, PASSWORD_ARGON2ID);
                }
                // 如果不支持Argon2id，回退到bcrypt
                return password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);
                
            case 'bcrypt':
            default:
                return password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);
        }
    }
    
    /**
     * 验证密码
     * @param string $password 原始密码
     * @param string $hash 存储的哈希值
     * @return bool 验证结果
     */
    public static function verifyPassword($password, $hash)
    {
        // 如果是旧的MD5密码，先验证MD5，然后升级
        if (strlen($hash) === 32 && ctype_xdigit($hash)) {
            return md5($password) === $hash;
        }
        
        return password_verify($password, $hash);
    }
    
    /**
     * 检查密码是否需要重新哈希
     * @param string $hash 当前哈希值
     * @param string $algorithm 算法
     * @return bool 是否需要重新哈希
     */
    public static function needsRehash($hash, $algorithm = 'bcrypt')
    {
        // MD5密码需要升级
        if (strlen($hash) === 32 && ctype_xdigit($hash)) {
            return true;
        }
        
        switch ($algorithm) {
            case 'argon2i':
                return defined('PASSWORD_ARGON2I') ? password_needs_rehash($hash, PASSWORD_ARGON2I) : false;
            case 'argon2id':
                return defined('PASSWORD_ARGON2ID') ? password_needs_rehash($hash, PASSWORD_ARGON2ID) : false;
            case 'bcrypt':
            default:
                return password_needs_rehash($hash, PASSWORD_BCRYPT, ['cost' => 12]);
        }
    }
    
    /**
     * 生成CSRF令牌
     * @return string CSRF令牌
     */
    public static function generateCsrfToken()
    {
        $token = bin2hex(random_bytes(32));
        Session::set('csrf_token', $token);
        return $token;
    }
    
    /**
     * 验证CSRF令牌
     * @param string $token 提交的令牌
     * @return bool 验证结果
     */
    public static function verifyCsrfToken($token)
    {
        $sessionToken = Session::get('csrf_token');
        return $sessionToken && hash_equals($sessionToken, $token);
    }
    
    /**
     * 获取CSRF令牌HTML
     * @return string HTML代码
     */
    public static function getCsrfTokenHtml()
    {
        $token = self::generateCsrfToken();
        return '<input type="hidden" name="csrf_token" value="' . $token . '">';
    }
    
    /**
     * XSS过滤
     * @param mixed $data 要过滤的数据
     * @param bool $deep 是否深度过滤数组
     * @return mixed 过滤后的数据
     */
    public static function filterXss($data, $deep = true)
    {
        if (is_array($data)) {
            if ($deep) {
                return array_map([self::class, 'filterXss'], $data);
            } else {
                return $data;
            }
        }
        
        if (is_string($data)) {
            // 移除危险的HTML标签和属性
            $data = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $data);
            $data = preg_replace('/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/mi', '', $data);
            $data = preg_replace('/javascript:/i', '', $data);
            $data = preg_replace('/on\w+\s*=/i', '', $data);
            
            // HTML实体编码
            return htmlspecialchars($data, ENT_QUOTES | ENT_HTML5, 'UTF-8');
        }
        
        return $data;
    }
    
    /**
     * SQL注入防护（参数化查询提醒）
     * @param string $sql SQL语句
     * @return bool 是否安全
     */
    public static function checkSqlSafety($sql)
    {
        // 检查是否包含潜在的SQL注入模式
        $dangerousPatterns = [
            '/union\s+select/i',
            '/drop\s+table/i',
            '/delete\s+from/i',
            '/insert\s+into/i',
            '/update\s+\w+\s+set/i',
            '/exec\s*\(/i',
            '/script\s*>/i'
        ];
        
        foreach ($dangerousPatterns as $pattern) {
            if (preg_match($pattern, $sql)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 生成安全的随机字符串
     * @param int $length 长度
     * @param string $chars 字符集
     * @return string 随机字符串
     */
    public static function generateRandomString($length = 32, $chars = null)
    {
        if ($chars === null) {
            $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        }
        
        $result = '';
        $charsLength = strlen($chars);
        
        for ($i = 0; $i < $length; $i++) {
            $result .= $chars[random_int(0, $charsLength - 1)];
        }
        
        return $result;
    }
    
    /**
     * IP地址验证和过滤
     * @param string $ip IP地址
     * @return string|false 过滤后的IP地址或false
     */
    public static function filterIp($ip)
    {
        return filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE);
    }
    
    /**
     * 检查请求频率限制
     * @param string $key 限制键
     * @param int $maxRequests 最大请求次数
     * @param int $timeWindow 时间窗口（秒）
     * @return bool 是否允许请求
     */
    public static function checkRateLimit($key, $maxRequests = 60, $timeWindow = 60)
    {
        $cacheKey = 'rate_limit_' . md5($key);
        $requests = Session::get($cacheKey, []);
        $now = time();
        
        // 清理过期的请求记录
        $requests = array_filter($requests, function($timestamp) use ($now, $timeWindow) {
            return ($now - $timestamp) < $timeWindow;
        });
        
        // 检查是否超过限制
        if (count($requests) >= $maxRequests) {
            return false;
        }
        
        // 记录当前请求
        $requests[] = $now;
        Session::set($cacheKey, $requests);
        
        return true;
    }
    
    /**
     * 安全的文件上传检查
     * @param array $file 上传的文件信息
     * @param array $allowedTypes 允许的文件类型
     * @param int $maxSize 最大文件大小（字节）
     * @return array 检查结果
     */
    public static function checkFileUpload($file, $allowedTypes = ['jpg', 'jpeg', 'png', 'gif'], $maxSize = 2097152)
    {
        $result = ['status' => false, 'message' => ''];
        
        // 检查文件是否上传成功
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $result['message'] = '文件上传失败';
            return $result;
        }
        
        // 检查文件大小
        if ($file['size'] > $maxSize) {
            $result['message'] = '文件大小超过限制';
            return $result;
        }
        
        // 检查文件类型
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, $allowedTypes)) {
            $result['message'] = '不允许的文件类型';
            return $result;
        }
        
        // 检查MIME类型
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        $allowedMimes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg', 
            'png' => 'image/png',
            'gif' => 'image/gif'
        ];
        
        if (!isset($allowedMimes[$extension]) || $mimeType !== $allowedMimes[$extension]) {
            $result['message'] = '文件类型不匹配';
            return $result;
        }
        
        $result['status'] = true;
        $result['message'] = '文件检查通过';
        return $result;
    }
}
