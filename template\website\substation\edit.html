<!DOCTYPE html>
<html>
<head>
	{include file="common_header" /}
	{include file="common_top" /}
</head>
<body>

  <div class="layui-fluid">
    <div class="layui-card">
      <div class="layui-card-body" style="padding: 15px;">
        <form class="layui-form" action="" lay-filter="component-form-group">
        

        <div class="layui-form-item">
            <label class="layui-form-label">所属群组</label>
            <div class="layui-input-block">
				<select name="su_g_id" id="su_g_id">
					<option value="0"> === 请选择群组 === </option>
					{volist name="Substationgroup" id="vo"}
						<option value="{$vo.su_g_id}" {if $info.su_g_id==$vo.su_g_id}selected{/if}>{$vo.su_g_title}</option>
					{/volist}
				</select>
            </div>
          </div> 
          
          
          <div class="layui-form-item">
            <label class="layui-form-label">分站备注</label>
            <div class="layui-input-block">
              <input name="su_content" type="text" class="layui-input" id="su_content"   placeholder="分站备注" value="{$info.su_content}">
            </div>
          </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">分站名称</label>
            <div class="layui-input-block">
              <input name="title" type="text" class="layui-input" id="title"   placeholder="分站名称" value="{$info.su_title}">
            </div>
          </div>   
  
          
          <div class="layui-form-item">
            <label class="layui-form-label">分站域名</label>
            <div class="layui-input-block">
              <input name="domain" type="text" class="layui-input" id="domain"   placeholder="分站域名" value="{$info.su_domain}">
            </div>
          </div> 
          
          
          <div class="layui-form-item">
            <label class="layui-form-label">分站帐号</label>
            <div class="layui-input-block">
              <input name="username" type="text" class="layui-input" id="username"   placeholder="分站帐号" value="{$info.su_name}">
            </div>
          </div> 
          
          <div class="layui-form-item">
            <label class="layui-form-label">分站密码</label>
            <div class="layui-input-block">
              <input name="password" type="text" class="layui-input" id="password"   placeholder="分站密码">
            </div>
          </div> 
          
          <div class="layui-form-item">
            <label class="layui-form-label">到期时间</label>
            <div class="layui-input-block">
              <input name="enddate" type="date" class="layui-input" id="enddate"   placeholder="到期时间" value="{$info.su_endtime}">
            </div>
          </div> 
          
          <div class="layui-form-item">
            <label class="layui-form-label">点卡数</label>
            <div class="layui-input-block">
              <input name="su_dk" type="number" class="layui-input" id="su_dk"   placeholder="点卡数" value="{$info.su_dk}">
            </div>
          </div>
          
          <div class="layui-form-item">
            <label class="layui-form-label">抽拥比</label>
            <div class="layui-input-block">
              <input name="su_dk_cd" type="number" class="layui-input" id="su_dk_cd"   placeholder="抽拥比" value="{$info.su_dk_cd}">
            </div>
          </div>
          
          
          <div class="layui-form-item">
            <label class="layui-form-label">分站权限</label>
            <div class="layui-input-block">
              <input type="radio" name="su_fzonoff" value="1" title="关闭" {if $info.su_fzonoff==1}checked=""{/if}>
              <input type="radio" name="su_fzonoff" value="2" title="开启" {if $info.su_fzonoff==2}checked=""{/if}>
            </div>
          </div>
          

           <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-block">
              <input type="radio" name="status" value="1" title="启用" {if $info.su_status==1}checked=""{/if}>
              <input type="radio" name="status" value="2" title="禁用" {if $info.su_status==2}checked=""{/if}>
              <input type="radio" name="status" value="3" title="到期" {if $info.su_status==3}checked=""{/if}>
            </div>
          </div>
          
		  
         <div class="layui-form-item layui-layout-admin">
              <div class="layui-footer" style="left: 0;">
                <div class="layui-btn sub">立即提交</div>
                <button type="reset" class="layui-btn layui-btn-primary ">重置</button>
              </div>
          </div>
        </form>
      </div>
    </div>
  </div>
{include file="common_footer" /} 
<script>
$(".sub").click(function(){
	//if(!$(".btn").hasClass("sub")){return false;}
	var su_g_id  = $("#su_g_id").val();
	var title    = $("#title").val();
	var domain   = $("#domain").val();
	var username = $("#username").val();
	var password = $("#password").val();
	var enddate  = $("#enddate").val();
	var status   = $("input[name='status']:checked").val();
    var su_fzonoff   = $("input[name='su_fzonoff']:checked").val();
    var su_dk = $("#su_dk").val();
    var su_dk_cd = $("#su_dk_cd").val();
    var su_content = $("#su_content").val();


	if(su_g_id==0){
		show_error("请先选择群组!");
		return false;
	}
	
	if(title==""){
		show_error("分站名称不能为空!");
		return false;
	}
	
	if(domain == ""){
		show_error("分站域名不能为空");
		return false;
	}
	
	
	if(username == ""){
		show_error("分站帐号不能为空");
		return false;
	}

	
	$.ajax({
		type:"POST",
		url:"{:url('substation/edit')}",
		dataType:"json",
		data:{
			id:"{$info.su_id}",
			su_g_id:su_g_id,
			title:title,
			domain:domain,
			username:username,
			password:password,
			status:status,
			enddate:enddate,
			su_dk:su_dk,
			su_dk_cd:su_dk_cd,
			su_fzonoff:su_fzonoff,
			su_content:su_content,
		},
		success:function(res){
			if(res.status == 1){
				window.parent.layer.closeAll();//关闭弹窗
				window.parent.location.reload();
				//show_toast_callurl(res.data,"{:url('device/index')}","success");
			}else{
				show_error(res.msg);
			}
		},
		error:function(jqXHR){
			console.log("Error: "+jqXHR.status);
		},
	});
	
});
</script>
</body>
</html>
