<?php
namespace app\common\validate;
use think\Validate;

class Substation extends Validate
{
    protected $rule = [
        // 登录验证规则
        'username'  => 'require|alphaNum|min:3|max:20',
        'password'  => 'require|min:6|max:32',
        
        // 添加/编辑分站验证规则
        'su_title'   => 'require|min:2|max:50',
        'su_domain'  => 'require|url|max:100',
        'su_g_id'    => 'require|integer|gt:0',
        'su_username' => 'require|alphaNum|min:3|max:20',
        'su_password' => 'require|min:6|max:32',
        'su_status'   => 'in:1,2',
        
        // 密码修改验证规则
        'oldpassword' => 'require|min:6|max:32',
        'newpassword' => 'require|min:6|max:32|different:oldpassword',
        'endpassword' => 'require|confirm:newpassword',
    ];

    protected $message = [
        // 登录验证消息
        'username.require'   => '用户名必须填写',
        'username.alphaNum'  => '用户名只能是字母和数字',
        'username.min'       => '用户名长度不能少于3位',
        'username.max'       => '用户名长度不能超过20位',
        'password.require'   => '密码必须填写',
        'password.min'       => '密码长度不能少于6位',
        'password.max'       => '密码长度不能超过32位',
        
        // 添加/编辑分站验证消息
        'su_title.require'   => '分站名称必须填写',
        'su_title.min'       => '分站名称长度不能少于2位',
        'su_title.max'       => '分站名称长度不能超过50位',
        'su_domain.require'  => '分站域名必须填写',
        'su_domain.url'      => '分站域名格式不正确',
        'su_domain.max'      => '分站域名长度不能超过100位',
        'su_g_id.require'    => '分站分组必须选择',
        'su_g_id.integer'    => '分站分组ID必须是整数',
        'su_g_id.gt'         => '分站分组ID必须大于0',
        'su_username.require' => '分站账号必须填写',
        'su_username.alphaNum' => '分站账号只能是字母和数字',
        'su_username.min'    => '分站账号长度不能少于3位',
        'su_username.max'    => '分站账号长度不能超过20位',
        'su_password.require' => '分站密码必须填写',
        'su_password.min'    => '分站密码长度不能少于6位',
        'su_password.max'    => '分站密码长度不能超过32位',
        'su_status.in'       => '分站状态值不正确',
        
        // 密码修改验证消息
        'oldpassword.require' => '原密码必须填写',
        'oldpassword.min'     => '原密码长度不能少于6位',
        'oldpassword.max'     => '原密码长度不能超过32位',
        'newpassword.require' => '新密码必须填写',
        'newpassword.min'     => '新密码长度不能少于6位',
        'newpassword.max'     => '新密码长度不能超过32位',
        'newpassword.different' => '新密码不能与原密码相同',
        'endpassword.require' => '确认密码必须填写',
        'endpassword.confirm' => '确认密码与新密码不一致',
    ];

    protected $scene = [
        'login'  => ['username', 'password'],
        'add'    => ['su_title', 'su_domain', 'su_g_id', 'su_username', 'su_password'],
        'edit'   => ['su_title', 'su_domain', 'su_g_id', 'su_username', 'su_status'],
        'changePassword' => ['oldpassword', 'newpassword', 'endpassword'],
    ];

    /**
     * 自定义验证规则：检查域名是否已存在
     */
    protected function checkDomainUnique($value, $rule, $data = [])
    {
        $model = new \app\common\model\Substation();
        $where = ['su_domain' => $value];
        
        // 编辑时排除当前记录
        if (isset($data['su_id']) && $data['su_id'] > 0) {
            $where['su_id'] = ['neq', $data['su_id']];
        }
        
        $exists = $model->where($where)->find();
        return $exists ? false : true;
    }

    /**
     * 自定义验证规则：检查用户名是否已存在
     */
    protected function checkUsernameUnique($value, $rule, $data = [])
    {
        $model = new \app\common\model\Substation();
        $where = ['su_username' => $value];
        
        // 编辑时排除当前记录
        if (isset($data['su_id']) && $data['su_id'] > 0) {
            $where['su_id'] = ['neq', $data['su_id']];
        }
        
        $exists = $model->where($where)->find();
        return $exists ? false : true;
    }
}
