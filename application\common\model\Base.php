<?php
namespace app\common\model;
use think\Model;
use think\Db;
use think\Cache;

class Base extends Model {
	
	protected $Prefix = "web_"; //表前缀

    //自定义初始化
    protected function initialize()
    {
        //需要调用`Model`的`initialize`方法
        parent::initialize();

        // 设置数据库表前缀
        $this->setTable($this->getTable());

        // 设置默认的查询字段
        if (empty($this->field)) {
            $this->field = true;
        }

        // 设置默认的软删除字段（如果需要）
        // $this->deleteTime = 'delete_time';

        // 设置默认的时间字段格式
        $this->autoWriteTimestamp = true;
        $this->createTime = 'addtime';
        $this->updateTime = 'updatetime';

        // 设置默认的数据过滤
        $this->readonly = ['addtime'];
    }
}