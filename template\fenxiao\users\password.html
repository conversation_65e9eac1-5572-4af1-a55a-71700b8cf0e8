<!DOCTYPE html>
<html>
<head>
	{include file="common_header" /}
	{include file="common_top" /}
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-header">修改密码</div>
          <div class="layui-card-body">
            
            <div class="layui-form" lay-filter="">
              <div class="layui-form-item">
                <label class="layui-form-label">当前密码</label>
                <div class="layui-input-inline">
                  <input type="password" name="oldpassword" id="oldpassword" class="layui-input">
                </div>
              </div>
              <div class="layui-form-item">
                <label class="layui-form-label">新密码</label>
                <div class="layui-input-inline">
                  <input type="password" name="password" id="password"  class="layui-input">
                </div>
                <div class="layui-form-mid layui-word-aux">6到16个字符</div>
              </div>
              <div class="layui-form-item">
                <label class="layui-form-label">确认新密码</label>
                <div class="layui-input-inline">
                  <input type="password" name="endpassword" id="endpassword"  class="layui-input">
                </div>
              </div>
              <div class="layui-form-item">
                <div class="layui-input-block">
                  <button class="layui-btn sub" lay-filter="setmypass">确认修改</button>
                </div>
              </div>
            </div>
            
          </div>
        </div>
      </div>
    </div>
  </div>
  {include file="common_footer" /}
  <script>
	$(".sub").click(function(){
		//if(!$(".btn").hasClass("sub")){return false;}
		var oldpassword = $("#oldpassword").val();
		var password    = $("#password").val();
		var endpassword = $("#endpassword").val();

		if(oldpassword == ""){
			show_error("旧密码不能为空!");
			return false
		}
		
		if(password == ""){
			show_error("新密码不能为空!");
			return false
		}
		
		if(endpassword == ""){
		show_error("确认密码不能为空!");
			return false
		}
		
		if(oldpassword == password){
			show_error("新密码不能和旧密码一致!");
			return false
		}

		if(password != endpassword){
			show_error("确认密码须与新密码一致!");
			return false
		}

		
		$.ajax({
			type:"POST",
			url:"{:url('users/password')}",
			dataType:"json",
			data:{
				oldpassword:oldpassword,
				password:password,
				endpassword:endpassword,
			},
			success:function(res){
				if(res.status == 1){
					show_toast_callurl(res.msg,"{:url('users/password')}");
				}else{
					show_error(res.msg);
				}
			},
			error:function(jqXHR){
				//console.log("Error: "+jqXHR.status);
			},
		});
		
	});
	

  </script>
</body>
</html>