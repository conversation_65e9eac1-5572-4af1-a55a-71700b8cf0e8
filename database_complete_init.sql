-- ========================================
-- 完整数据库初始化脚本
-- 包含所有业务表结构
-- ========================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 用户表
CREATE TABLE IF NOT EXISTS `web_users` (
  `u_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `u_phone` varchar(20) NOT NULL COMMENT '手机号',
  `u_password` varchar(64) NOT NULL COMMENT '密码',
  `u_nickname` varchar(50) DEFAULT '' COMMENT '昵称',
  `u_status` tinyint(1) DEFAULT 1 COMMENT '状态 1:正常 2:禁用',
  `u_supermanage` tinyint(1) DEFAULT 1 COMMENT '超管状态 1:普通 2:超级管理员',
  `u_count` int(11) DEFAULT 0 COMMENT '登录次数',
  `u_regtime` datetime DEFAULT NULL COMMENT '注册时间',
  `u_this_time` datetime DEFAULT NULL COMMENT '本次登录时间',
  `u_this_ip` varchar(50) DEFAULT '' COMMENT '本次登录IP',
  `u_last_time` datetime DEFAULT NULL COMMENT '上次登录时间',
  `u_last_ip` varchar(50) DEFAULT '' COMMENT '上次登录IP',
  PRIMARY KEY (`u_id`),
  UNIQUE KEY `uk_phone` (`u_phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 用户登录日志表
CREATE TABLE IF NOT EXISTS `web_users_logs` (
  `ul_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `u_id` int(11) NOT NULL COMMENT '用户ID',
  `ul_type` tinyint(1) DEFAULT 1 COMMENT '操作类型 1:登录',
  `ul_addtime` datetime NOT NULL COMMENT '操作时间',
  `ul_ip` varchar(50) DEFAULT '' COMMENT '操作IP',
  PRIMARY KEY (`ul_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户登录日志表';

-- 权限规则表
CREATE TABLE IF NOT EXISTS `web_auth_rule` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT COMMENT '规则ID',
  `name` char(80) NOT NULL DEFAULT '' COMMENT '规则唯一标识',
  `title` char(20) NOT NULL DEFAULT '' COMMENT '规则中文名称',
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '规则类型',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
  `condition` char(100) NOT NULL DEFAULT '' COMMENT '规则表达式',
  `said` int(11) DEFAULT 0 COMMENT '上级ID',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限规则表';

-- 权限分组表
CREATE TABLE IF NOT EXISTS `web_auth_group` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT COMMENT '分组ID',
  `title` char(100) NOT NULL DEFAULT '' COMMENT '分组名称',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
  `rules` char(80) NOT NULL DEFAULT '' COMMENT '规则ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限分组表';

-- 权限用户组关联表
CREATE TABLE IF NOT EXISTS `web_auth_group_access` (
  `uid` mediumint(8) unsigned NOT NULL COMMENT '用户ID',
  `group_id` mediumint(8) unsigned NOT NULL COMMENT '分组ID',
  UNIQUE KEY `uid_group_id` (`uid`,`group_id`),
  KEY `uid` (`uid`),
  KEY `group_id` (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限分组用户表';

-- 分站表
CREATE TABLE IF NOT EXISTS `web_substation` (
  `su_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分站ID',
  `su_g_id` int(11) NOT NULL DEFAULT 0 COMMENT '分站群组ID',
  `su_title` varchar(100) NOT NULL COMMENT '分站名称',
  `su_domain` varchar(100) NOT NULL COMMENT '分站域名',
  `su_username` varchar(50) NOT NULL COMMENT '分站账号',
  `su_password` varchar(64) NOT NULL COMMENT '分站密码',
  `su_status` tinyint(1) DEFAULT 1 COMMENT '状态 1:正常 2:禁用',
  `su_addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `su_updatetime` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`su_id`),
  KEY `idx_group` (`su_g_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分站表';

-- 分站分组表
CREATE TABLE IF NOT EXISTS `web_substationgroup` (
  `su_g_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分站分组ID',
  `su_g_title` varchar(100) NOT NULL COMMENT '分组名称',
  `su_g_day` int(11) DEFAULT 0 COMMENT '天数',
  `su_g_paylist` text COMMENT '支付列表',
  `su_g_content` text COMMENT '描述',
  `su_g_addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  PRIMARY KEY (`su_g_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分站分组表';

-- 分销表
CREATE TABLE IF NOT EXISTS `web_distribution` (
  `di_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分销ID',
  `di_g_id` int(11) NOT NULL DEFAULT 0 COMMENT '分销群组ID',
  `di_su_id` int(11) NOT NULL DEFAULT 0 COMMENT '所属分站ID',
  `di_title` varchar(100) NOT NULL COMMENT '分销名称',
  `di_username` varchar(50) NOT NULL COMMENT '分销账号',
  `di_password` varchar(64) NOT NULL COMMENT '分销密码',
  `di_status` tinyint(1) DEFAULT 1 COMMENT '状态 1:正常 2:禁用',
  `di_addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  PRIMARY KEY (`di_id`),
  KEY `idx_group` (`di_g_id`),
  KEY `idx_substation` (`di_su_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销表';

-- 分销分组表
CREATE TABLE IF NOT EXISTS `web_distributiongroup` (
  `di_g_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分销分组ID',
  `di_g_title` varchar(100) NOT NULL COMMENT '分组名称',
  `di_g_day` int(11) DEFAULT 0 COMMENT '天数',
  `di_g_content` text COMMENT '描述',
  `di_g_addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  PRIMARY KEY (`di_g_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销分组表';

-- 微信群组表
CREATE TABLE IF NOT EXISTS `web_wxgroup` (
  `wg_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '微信群ID',
  `wg_title` varchar(100) NOT NULL COMMENT '群名称',
  `wg_qrcode` varchar(255) DEFAULT '' COMMENT '群二维码',
  `wg_count` int(11) DEFAULT 0 COMMENT '群人数',
  `wg_max_count` int(11) DEFAULT 500 COMMENT '群最大人数',
  `wg_status` tinyint(1) DEFAULT 1 COMMENT '状态 1:正常 2:满员 3:禁用',
  `wg_addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  PRIMARY KEY (`wg_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信群组表';

-- 微信群模板表
CREATE TABLE IF NOT EXISTS `web_wxgrouptmp` (
  `wt_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `wt_title` varchar(100) NOT NULL COMMENT '模板名称',
  `wt_content` text COMMENT '模板内容',
  `wt_status` tinyint(1) DEFAULT 1 COMMENT '状态',
  `wt_addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  PRIMARY KEY (`wt_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信群模板表';

-- 账单表
CREATE TABLE IF NOT EXISTS `web_bill` (
  `bi_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '账单ID',
  `bi_user_type` tinyint(1) NOT NULL COMMENT '用户类型 1:分站 2:分销',
  `bi_user_id` int(11) NOT NULL COMMENT '用户ID',
  `bi_type` tinyint(1) NOT NULL COMMENT '账单类型 1:充值 2:消费',
  `bi_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '金额',
  `bi_balance` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '余额',
  `bi_desc` varchar(255) DEFAULT '' COMMENT '描述',
  `bi_addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  PRIMARY KEY (`bi_id`),
  KEY `idx_user` (`bi_user_type`, `bi_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='账单表';

-- 支付列表表
CREATE TABLE IF NOT EXISTS `web_paylist` (
  `pl_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '支付ID',
  `pl_title` varchar(100) NOT NULL COMMENT '支付名称',
  `pl_type` varchar(50) NOT NULL COMMENT '支付类型',
  `pl_config` text COMMENT '支付配置',
  `pl_status` tinyint(1) DEFAULT 1 COMMENT '状态',
  `pl_addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  PRIMARY KEY (`pl_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付列表表';

-- 点卡表
CREATE TABLE IF NOT EXISTS `web_dianka` (
  `dk_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '点卡ID',
  `dk_card_no` varchar(50) NOT NULL COMMENT '卡号',
  `dk_card_pass` varchar(50) NOT NULL COMMENT '卡密',
  `dk_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '面值',
  `dk_status` tinyint(1) DEFAULT 1 COMMENT '状态 1:未使用 2:已使用',
  `dk_use_time` datetime DEFAULT NULL COMMENT '使用时间',
  `dk_use_user_id` int(11) DEFAULT NULL COMMENT '使用用户ID',
  `dk_addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  PRIMARY KEY (`dk_id`),
  UNIQUE KEY `uk_card` (`dk_card_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='点卡表';

-- 点卡使用记录表
CREATE TABLE IF NOT EXISTS `web_diankalog` (
  `dl_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `dl_dk_id` int(11) NOT NULL COMMENT '点卡ID',
  `dl_user_type` tinyint(1) NOT NULL COMMENT '用户类型',
  `dl_user_id` int(11) NOT NULL COMMENT '用户ID',
  `dl_amount` decimal(10,2) NOT NULL COMMENT '金额',
  `dl_addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '使用时间',
  PRIMARY KEY (`dl_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='点卡使用记录表';

-- 抽佣表
CREATE TABLE IF NOT EXISTS `web_chouyong` (
  `cy_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '抽佣ID',
  `cy_user_type` tinyint(1) NOT NULL COMMENT '用户类型',
  `cy_user_id` int(11) NOT NULL COMMENT '用户ID',
  `cy_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '抽佣金额',
  `cy_rate` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '抽佣比例',
  `cy_desc` varchar(255) DEFAULT '' COMMENT '描述',
  `cy_addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  PRIMARY KEY (`cy_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='抽佣表';

-- 对账表
CREATE TABLE IF NOT EXISTS `web_duizhang` (
  `dz_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '对账ID',
  `dz_user_type` tinyint(1) NOT NULL COMMENT '用户类型',
  `dz_user_id` int(11) NOT NULL COMMENT '用户ID',
  `dz_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '金额',
  `dz_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '类型 1:收入 2:支出',
  `dz_desc` varchar(255) DEFAULT '' COMMENT '描述',
  `dz_addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  PRIMARY KEY (`dz_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='对账表';

-- 分销提现表
CREATE TABLE IF NOT EXISTS `web_distributiontixian` (
  `dt_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '提现ID',
  `dt_user_id` int(11) NOT NULL COMMENT '用户ID',
  `dt_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '提现金额',
  `dt_status` tinyint(1) DEFAULT 1 COMMENT '状态 1:申请 2:通过 3:拒绝',
  `dt_desc` varchar(255) DEFAULT '' COMMENT '备注',
  `dt_addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `dt_handle_time` datetime DEFAULT NULL COMMENT '处理时间',
  PRIMARY KEY (`dt_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销提现表';

-- 分站提现表
CREATE TABLE IF NOT EXISTS `web_substationtixian` (
  `st_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '提现ID',
  `st_user_id` int(11) NOT NULL COMMENT '分站ID',
  `st_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '提现金额',
  `st_status` tinyint(1) DEFAULT 1 COMMENT '状态 1:申请 2:通过 3:拒绝',
  `st_desc` varchar(255) DEFAULT '' COMMENT '备注',
  `st_addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `st_handle_time` datetime DEFAULT NULL COMMENT '处理时间',
  PRIMARY KEY (`st_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分站提现表';

-- 导航表
CREATE TABLE IF NOT EXISTS `web_navigat` (
  `nav_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '导航ID',
  `nav_title` varchar(100) NOT NULL COMMENT '导航标题',
  `nav_url` varchar(255) NOT NULL COMMENT '链接地址',
  `nav_icon` varchar(100) DEFAULT '' COMMENT '图标',
  `nav_sort` int(11) DEFAULT 0 COMMENT '排序',
  `nav_status` tinyint(1) DEFAULT 1 COMMENT '状态',
  `nav_addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  PRIMARY KEY (`nav_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='导航表';

-- 导航分组关联表
CREATE TABLE IF NOT EXISTS `web_navigat_group` (
  `ng_id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `nav_id` int(11) NOT NULL COMMENT '导航ID',
  `group_id` int(11) NOT NULL COMMENT '分组ID',
  PRIMARY KEY (`ng_id`),
  KEY `idx_nav` (`nav_id`),
  KEY `idx_group` (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='导航分组关联表';

-- 语音列表表
CREATE TABLE IF NOT EXISTS `web_voicelist` (
  `vol_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '语音ID',
  `vol_title` varchar(100) NOT NULL COMMENT '语音标题',
  `vol_content` text COMMENT '语音内容/描述',
  `vol_file_path` varchar(255) DEFAULT '' COMMENT '语音文件路径',
  `vol_status` tinyint(1) DEFAULT 1 COMMENT '状态 1:正常 2:禁用',
  `vol_addtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `vol_updatetime` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`vol_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='语音列表表';

-- 系统配置表
CREATE TABLE IF NOT EXISTS `web_system_config` (
  `config_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(50) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_type` varchar(20) DEFAULT 'string' COMMENT '配置类型',
  `config_group` varchar(30) DEFAULT 'system' COMMENT '配置分组',
  `config_desc` varchar(100) DEFAULT '' COMMENT '配置说明',
  `is_readonly` tinyint(1) DEFAULT 0 COMMENT '是否只读',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`config_id`),
  UNIQUE KEY `uk_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 操作日志表
CREATE TABLE IF NOT EXISTS `web_operation_logs` (
  `ol_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `ol_user_type` tinyint(1) NOT NULL COMMENT '用户类型 1:管理员 2:分站 3:分销',
  `ol_user_id` int(11) NOT NULL COMMENT '用户ID',
  `ol_module` varchar(30) DEFAULT '' COMMENT '模块名',
  `ol_controller` varchar(30) DEFAULT '' COMMENT '控制器名',
  `ol_action` varchar(30) DEFAULT '' COMMENT '操作名',
  `ol_method` varchar(10) DEFAULT '' COMMENT '请求方法',
  `ol_params` text COMMENT '请求参数',
  `ol_ip` varchar(50) DEFAULT '' COMMENT '操作IP',
  `ol_user_agent` varchar(255) DEFAULT '' COMMENT '用户代理',
  `ol_addtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`ol_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

-- 系统监控表
CREATE TABLE IF NOT EXISTS `web_system_monitor` (
  `sm_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '监控ID',
  `sm_type` varchar(30) NOT NULL COMMENT '监控类型',
  `sm_data` text COMMENT '监控数据',
  `sm_status` tinyint(1) DEFAULT 1 COMMENT '状态',
  `sm_addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间',
  PRIMARY KEY (`sm_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统监控表';

SET FOREIGN_KEY_CHECKS = 1;