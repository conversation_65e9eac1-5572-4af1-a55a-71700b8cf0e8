<?php
/**
 * 简单数据库修复脚本
 * 直接使用PDO连接数据库进行修复
 */

echo "=== 数据库修复脚本开始执行 ===\n";

// 直接使用数据库配置
$host = '127.0.0.1';
$dbname = 'qu_qinghu123_com';
$user = 'qu_qinghu123_com';
$pass = 'qu_qinghu123_com';

echo "数据库配置:\n";
echo "主机: {$host}\n";
echo "数据库: {$dbname}\n";
echo "用户: {$user}\n\n";

try {
    // 连接数据库
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8mb4";
    $pdo = new PDO($dsn, $user, $pass, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "✅ 数据库连接成功\n\n";
    
    // 1. 检查分站表结构
    echo "1. 检查分站表结构...\n";
    $stmt = $pdo->query("SHOW COLUMNS FROM web_substation");
    $existingColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "当前分站表字段: " . implode(', ', $existingColumns) . "\n";
    
    // 需要添加的字段
    $requiredFields = [
        'su_fz_money' => "ALTER TABLE `web_substation` ADD COLUMN `su_fz_money` decimal(10,2) DEFAULT 0.00 COMMENT '分站资金'",
        'su_s_id' => "ALTER TABLE `web_substation` ADD COLUMN `su_s_id` int(11) DEFAULT 0 COMMENT '上级分站ID'",
        'su_endtime' => "ALTER TABLE `web_substation` ADD COLUMN `su_endtime` date DEFAULT NULL COMMENT '到期时间'",
        'su_name' => "ALTER TABLE `web_substation` ADD COLUMN `su_name` varchar(50) DEFAULT '' COMMENT '分站账号别名'",
        'su_pass' => "ALTER TABLE `web_substation` ADD COLUMN `su_pass` varchar(64) DEFAULT '' COMMENT '分站密码别名'",
        'su_dk' => "ALTER TABLE `web_substation` ADD COLUMN `su_dk` decimal(10,2) DEFAULT 0.00 COMMENT '点卡余额'",
        'su_dk_cd' => "ALTER TABLE `web_substation` ADD COLUMN `su_dk_cd` decimal(5,2) DEFAULT 0.00 COMMENT '抽佣比例'"
    ];
    
    foreach ($requiredFields as $field => $sql) {
        if (!in_array($field, $existingColumns)) {
            echo "添加字段: {$field}...\n";
            $pdo->exec($sql);
            echo "✅ 字段 {$field} 添加成功\n";
        } else {
            echo "⏭️ 字段 {$field} 已存在，跳过\n";
        }
    }
    
    // 同步数据到别名字段
    echo "\n2. 同步分站表数据...\n";
    $stmt1 = $pdo->exec("UPDATE `web_substation` SET `su_name` = `su_username` WHERE `su_name` = '' OR `su_name` IS NULL");
    $stmt2 = $pdo->exec("UPDATE `web_substation` SET `su_pass` = `su_password` WHERE `su_pass` = '' OR `su_pass` IS NULL");
    echo "✅ 同步了 {$stmt1} 条分站账号数据\n";
    echo "✅ 同步了 {$stmt2} 条分站密码数据\n";
    
    // 3. 检查分销表结构
    echo "\n3. 检查分销表结构...\n";
    $stmt = $pdo->query("SHOW COLUMNS FROM web_distribution");
    $existingDistColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "当前分销表字段: " . implode(', ', $existingDistColumns) . "\n";
    
    // 需要添加的分销表字段
    $requiredDistFields = [
        'du_name' => "ALTER TABLE `web_distribution` ADD COLUMN `du_name` varchar(50) DEFAULT '' COMMENT '分销账号别名'",
        'du_pass' => "ALTER TABLE `web_distribution` ADD COLUMN `du_pass` varchar(64) DEFAULT '' COMMENT '分销密码别名'",
        'du_id' => "ALTER TABLE `web_distribution` ADD COLUMN `du_id` int(11) DEFAULT 0 COMMENT '分销ID别名'",
        'su_id' => "ALTER TABLE `web_distribution` ADD COLUMN `su_id` int(11) DEFAULT 0 COMMENT '所属分站ID别名'",
        'du_status' => "ALTER TABLE `web_distribution` ADD COLUMN `du_status` tinyint(1) DEFAULT 1 COMMENT '分销状态别名'"
    ];
    
    foreach ($requiredDistFields as $field => $sql) {
        if (!in_array($field, $existingDistColumns)) {
            echo "添加字段: {$field}...\n";
            $pdo->exec($sql);
            echo "✅ 字段 {$field} 添加成功\n";
        } else {
            echo "⏭️ 字段 {$field} 已存在，跳过\n";
        }
    }
    
    // 同步分销表数据
    echo "\n4. 同步分销表数据...\n";
    $stmt3 = $pdo->exec("UPDATE `web_distribution` SET `du_name` = `di_username` WHERE `du_name` = '' OR `du_name` IS NULL");
    $stmt4 = $pdo->exec("UPDATE `web_distribution` SET `du_pass` = `di_password` WHERE `du_pass` = '' OR `du_pass` IS NULL");
    $stmt5 = $pdo->exec("UPDATE `web_distribution` SET `du_id` = `di_id` WHERE `du_id` = 0 OR `du_id` IS NULL");
    $stmt6 = $pdo->exec("UPDATE `web_distribution` SET `su_id` = `di_su_id` WHERE `su_id` = 0 OR `su_id` IS NULL");
    $stmt7 = $pdo->exec("UPDATE `web_distribution` SET `du_status` = `di_status` WHERE `du_status` IS NULL");
    
    echo "✅ 同步了 {$stmt3} 条分销账号数据\n";
    echo "✅ 同步了 {$stmt4} 条分销密码数据\n";
    echo "✅ 同步了 {$stmt5} 条分销ID数据\n";
    echo "✅ 同步了 {$stmt6} 条分站关联数据\n";
    echo "✅ 同步了 {$stmt7} 条分销状态数据\n";
    
    // 5. 检查并创建微信群表
    echo "\n5. 检查微信群表...\n";
    $stmt = $pdo->query("SHOW TABLES LIKE 'web_wxgroup'");
    $wxgroupExists = $stmt->fetch();
    
    if (!$wxgroupExists) {
        echo "创建微信群表...\n";
        $createWxgroupSql = "
        CREATE TABLE `web_wxgroup` (
            `wxg_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '微信群ID',
            `su_id` int(11) NOT NULL DEFAULT 0 COMMENT '所属分站ID',
            `du_id` int(11) NOT NULL DEFAULT 0 COMMENT '所属分销ID',
            `wxg_title` varchar(100) NOT NULL COMMENT '群名称',
            `wxg_qrcode` varchar(255) DEFAULT '' COMMENT '群二维码',
            `wxg_count` int(11) DEFAULT 0 COMMENT '群人数',
            `wxg_max_count` int(11) DEFAULT 500 COMMENT '群最大人数',
            `wxg_status` tinyint(1) DEFAULT 1 COMMENT '状态 1:正常 2:满员 3:禁用',
            `wxg_addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
            `wxg_updatetime` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`wxg_id`),
            KEY `idx_su_id` (`su_id`),
            KEY `idx_du_id` (`du_id`),
            KEY `idx_status` (`wxg_status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信群表'";
        
        $pdo->exec($createWxgroupSql);
        echo "✅ 微信群表创建成功\n";
    } else {
        echo "⏭️ 微信群表已存在，跳过\n";
    }
    
    // 6. 验证修复结果
    echo "\n6. 验证修复结果...\n";
    
    // 检查分站表字段
    $stmt = $pdo->query("SHOW COLUMNS FROM web_substation");
    $newSubColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $missingSubFields = array_diff(array_keys($requiredFields), $newSubColumns);
    if (empty($missingSubFields)) {
        echo "✅ 分站表字段修复完成\n";
    } else {
        echo "❌ 分站表仍缺少字段: " . implode(', ', $missingSubFields) . "\n";
    }
    
    // 检查分销表字段
    $stmt = $pdo->query("SHOW COLUMNS FROM web_distribution");
    $newDistColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $missingDistFields = array_diff(array_keys($requiredDistFields), $newDistColumns);
    if (empty($missingDistFields)) {
        echo "✅ 分销表字段修复完成\n";
    } else {
        echo "❌ 分销表仍缺少字段: " . implode(', ', $missingDistFields) . "\n";
    }
    
    // 7. 测试查询
    echo "\n7. 测试修复后的查询...\n";
    
    // 测试分站查询
    try {
        $stmt = $pdo->query("SELECT su_id, su_name, su_username FROM web_substation LIMIT 1");
        $substationTest = $stmt->fetch();
        if ($substationTest) {
            echo "✅ 分站表查询测试成功\n";
            echo "测试数据: su_id={$substationTest['su_id']}, su_name={$substationTest['su_name']}, su_username={$substationTest['su_username']}\n";
        } else {
            echo "⚠️ 分站表无数据，但查询结构正常\n";
        }
    } catch (Exception $e) {
        echo "❌ 分站表查询测试失败: " . $e->getMessage() . "\n";
    }
    
    // 测试分销查询
    try {
        $stmt = $pdo->query("SELECT di_id, du_id, du_name, di_username FROM web_distribution LIMIT 1");
        $distributionTest = $stmt->fetch();
        if ($distributionTest) {
            echo "✅ 分销表查询测试成功\n";
            echo "测试数据: di_id={$distributionTest['di_id']}, du_id={$distributionTest['du_id']}, du_name={$distributionTest['du_name']}\n";
        } else {
            echo "⚠️ 分销表无数据，但查询结构正常\n";
        }
    } catch (Exception $e) {
        echo "❌ 分销表查询测试失败: " . $e->getMessage() . "\n";
    }
    
    echo "\n=== 数据库修复脚本执行完成 ===\n";
    echo "✅ 所有修复操作已完成！\n";
    echo "📋 修复总结:\n";
    echo "   - 分站表字段修复完成\n";
    echo "   - 分销表字段修复完成\n";
    echo "   - 微信群表检查完成\n";
    echo "   - 数据同步完成\n";
    echo "   - 查询测试完成\n\n";
    
    echo "🎉 系统现在可以正常运行了！\n";
    
} catch (Exception $e) {
    echo "❌ 修复过程中出现错误: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . "\n";
    echo "错误行号: " . $e->getLine() . "\n";
    exit(1);
}
