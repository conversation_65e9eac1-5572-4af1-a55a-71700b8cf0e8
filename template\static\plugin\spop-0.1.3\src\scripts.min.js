!function(){"use strict";function t(t,e){return"string"==typeof t?(e||document).getElementById(t):t||null}function e(t,e){return"string"==typeof t?(e||document).querySelectorAll(t):t||null}function n(t,e){for(var n=0;n<t.length;n++)e.call(n,t[n])}function o(t,e){t.classList?t.classList.remove(e):t.className=t.className.replace(new RegExp("(^|\\b)"+e.split(" ").join("|")+"(\\b|$)","gi")," ")}var s={};s.basic=function(){t("code-markup").innerHTML='&lt;link href="spop.css" rel="stylesheet"&gt;\n&lt;!-- Even better, SamallPop is made with scss,\n     @import to your style.scss --&gt;\n&lt;script src="spop.js"&gt;&lt;/script&gt;',t("btn-default-pop").addEventListener("click",function(){spop("Default SmallPop")},!1),t("btn-success-pop").addEventListener("click",function(){spop('<h4 class="spop-title">Success</h4>Iˈm a success SmallPop',"success")},!1),t("btn-warning-pop").addEventListener("click",function(){spop("Warning SmallPop","warning")},!1),t("btn-error-pop").addEventListener("click",function(){spop("<strong>Error Here!</strong>","error")},!1),t("code-basic").innerHTML="spop('Default SmallPop');\n\nspop('&lt;h4 class=\"spop-title\">Success&lt;/h4>Iˈm a success SmallPop', 'success');\n\nspop('Warning SmallPop', 'warning');\n\nspop('&lt;strong&gt;Error Here!&lt;/strong&gt;', 'error');"}(),s.position=function(){t("btn-top-left").addEventListener("click",function(){spop({template:"Position top left",position:"top-left",style:"success"})},!1),t("code-position").innerHTML="// top left example\nspop({\n	template: 'Position top left',\n	position  : 'top-left',\n	style: 'success'\n});'",t("btn-top-center").addEventListener("click",function(){spop({template:"Position top center",position:"top-center"})},!1),t("btn-top-right").addEventListener("click",function(){spop({template:"Position top right",position:"top-right"})},!1),t("btn-bottom-left").addEventListener("click",function(){spop({template:"Position bottom left",position:"bottom-left",style:"error"})},!1),t("btn-bottom-center").addEventListener("click",function(){spop({template:"Position bottom center",position:"bottom-center"})},!1),t("btn-bottom-right").addEventListener("click",function(){spop({template:"Position bottom right",position:"bottom-right"})},!1)}(),s.autoclose=function(){t("btn-autoclose-pop").addEventListener("click",function(){spop({template:"3 seconds autoclose",autoclose:3e3})},!1),t("code-autoclose-pop").innerHTML="spop({\n	template: '3 seconds autoclose',\n	autoclose: 3000\n});"}(),s.groups=function(){t("btn-groups-1").addEventListener("click",function(){spop({template:"All fields are required!",group:"submit-satus",style:"error"})},!1),t("btn-groups").addEventListener("click",function(){spop("Nothing here...")},!1),t("btn-groups-2").addEventListener("click",function(){spop({template:"Your information has been submitted.",group:"submit-satus",style:"success",autoclose:2e3})},!1),t("code-groups").innerHTML="spop({\n	template: 'All fields are required!',\n	group: 'submit-satus',\n	style: 'error'\n});\n\nspop('Nothing here...');\n\nspop({\n	template: 'Your information has been submitted',\n	group: 'submit-satus',\n	style: 'success'\n	autoclose: 2000\n});"}(),s.callbacks=function(){t("btn-callbacks").addEventListener("click",function(){spop({template:"Please, close me.",style:"warning",onOpen:function(){document.body.style.background="#fff"},onClose:function(){document.body.style.background="",spop({template:"Thank you!",style:"success",autoclose:2e3})}})},!1),t("code-callbacks").innerHTML="spop({\n	template: 'Please, close me.',\n	style:'warning',\n	onOpen: function () {\n		document.body.style.background = \"#fff\";\n	},\n	onClose: function() {\n		document.body.style.background = \"\";\n		spop({\n			template: 'Thank you!',\n			style: 'success',\n			autoclose: 2000\n		});\n	}\n});\n"}(),s.events=function(){t("btn-event").addEventListener("click",function(){spop('Got to <a href="#demo-defaults" data-spop="close">defaults</a>')},!1),t("code-event").innerHTML='spop(\'Got to &lt;a href="#demo-defaults" data-spop="close"&gt;defaults&lt;/a&gt;\');'}(),s.defaults=function(){t("btn-defaults").addEventListener("click",function(){spop.defaults={style:"error",autoclose:5e3,position:"top-left"},spop("Defaults changed! See the others examples.")},!1),t("btn-defaults-reset").addEventListener("click",function(){spop.defaults={},spop("Defauls restored")},!1),t("code-defaults").innerHTML="spop.defaults = {\n	style     : 'error',\n	autoclose : 5000,\n	position  : 'top-left'\n};\n\nspop('Defaults changed! See the others examples.');\n"}(),s.options=function(){t("code-options").innerHTML="spop({\n	template  : null,// string required. Without it nothing happens!\n	style     : 'info',// error or success\n	autoclose : false,// miliseconds\n	position  : 'top-right',// top-left top-center bottom-left bottom-center bottom-right\n	icon      : true,// or false\n	group     : false,// string, add a id reference \n	onOpen    : funtion() { },\n	onClose   : funtion() { }\n});"}(),s.removeAllPops=function(){var t,s,i=e(".title"),p=function(){t=e(".spop"),s=e(".spop-container"),n(t,function(t){o(t,"spop--in")}),setTimeout(function(){n(s,function(t){t.parentNode.removeChild(t)})},300)};n(i,function(t){t.addEventListener("click",p,!1)})}()}();