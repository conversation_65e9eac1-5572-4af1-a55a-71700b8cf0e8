<?php
namespace app\common\middleware;

use app\common\service\SecurityService;
use think\Request;

/**
 * CSRF中间件类
 * 用于防护跨站请求伪造攻击
 */
class CsrfMiddleware
{
    /**
     * 处理请求
     * @param \think\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle($request, \Closure $next)
    {
        // 只对POST、PUT、DELETE请求进行CSRF检查
        if (in_array($request->method(), ['POST', 'PUT', 'DELETE'])) {
            $token = $request->param('csrf_token') ?: $request->header('X-CSRF-TOKEN');
            
            if (!SecurityService::verifyCsrfToken($token)) {
                if ($request->isAjax()) {
                    return json(['status' => 403, 'msg' => 'CSRF token验证失败']);
                } else {
                    abort(403, 'CSRF token验证失败');
                }
            }
        }
        
        return $next($request);
    }
}
