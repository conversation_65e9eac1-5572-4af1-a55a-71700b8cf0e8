<?php
namespace app\common\service;

use think\Db;

/**
 * 数据库兼容性处理类
 * 处理字段名不匹配的问题
 */
class DatabaseCompatibility
{
    /**
     * 字段映射表
     */
    private static $fieldMapping = [
        'web_substation' => [
            'su_name' => 'su_username',
            'su_pass' => 'su_password'
        ],
        'web_distribution' => [
            'du_id' => 'di_id',
            'du_name' => 'di_username',
            'du_pass' => 'di_password',
            'du_status' => 'di_status',
            'su_id' => 'di_su_id'
        ]
    ];
    
    /**
     * 转换查询条件中的字段名
     * @param string $table 表名
     * @param array $where 查询条件
     * @return array 转换后的查询条件
     */
    public static function convertWhereFields($table, $where)
    {
        if (!isset(self::$fieldMapping[$table])) {
            return $where;
        }
        
        $mapping = self::$fieldMapping[$table];
        $newWhere = [];
        
        foreach ($where as $field => $value) {
            if (isset($mapping[$field])) {
                $newWhere[$mapping[$field]] = $value;
            } else {
                $newWhere[$field] = $value;
            }
        }
        
        return $newWhere;
    }
    
    /**
     * 转换查询结果中的字段名
     * @param string $table 表名
     * @param array $data 查询结果
     * @return array 转换后的结果
     */
    public static function convertResultFields($table, $data)
    {
        if (!isset(self::$fieldMapping[$table])) {
            return $data;
        }
        
        $mapping = array_flip(self::$fieldMapping[$table]);
        
        foreach ($mapping as $dbField => $codeField) {
            if (isset($data[$dbField]) && !isset($data[$codeField])) {
                $data[$codeField] = $data[$dbField];
            }
        }
        
        return $data;
    }
    
    /**
     * 检查表是否存在指定字段
     * @param string $table 表名
     * @param string $field 字段名
     * @return bool
     */
    public static function hasField($table, $field)
    {
        try {
            $columns = Db::query("SHOW COLUMNS FROM {$table}");
            $fieldNames = array_column($columns, 'Field');
            return in_array($field, $fieldNames);
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 获取兼容的字段名
     * @param string $table 表名
     * @param string $field 字段名
     * @return string 实际存在的字段名
     */
    public static function getCompatibleField($table, $field)
    {
        // 先检查原字段名是否存在
        if (self::hasField($table, $field)) {
            return $field;
        }
        
        // 检查映射字段是否存在
        if (isset(self::$fieldMapping[$table][$field])) {
            $mappedField = self::$fieldMapping[$table][$field];
            if (self::hasField($table, $mappedField)) {
                return $mappedField;
            }
        }
        
        return $field; // 返回原字段名
    }
}