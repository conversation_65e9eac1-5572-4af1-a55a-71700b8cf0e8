<?php
/**
 * 数据库修复脚本
 * 直接通过PHP执行数据库修复操作
 */

// 引入ThinkPHP框架
require_once 'thinkphp/start.php';

use think\Db;
use think\Config;

echo "=== 数据库修复脚本开始执行 ===\n";

try {
    // 检查数据库连接
    echo "1. 检查数据库连接...\n";
    $dbConfig = Config::get('database');
    echo "数据库类型: " . $dbConfig['type'] . "\n";
    echo "数据库主机: " . $dbConfig['hostname'] . "\n";
    echo "数据库名称: " . $dbConfig['database'] . "\n";
    
    // 测试连接
    $testQuery = Db::query("SELECT 1 as test");
    if ($testQuery) {
        echo "✅ 数据库连接成功\n\n";
    }
    
    // 2. 检查分站表结构
    echo "2. 检查分站表结构...\n";
    $substationColumns = Db::query("SHOW COLUMNS FROM web_substation");
    $existingColumns = array_column($substationColumns, 'Field');
    
    echo "当前分站表字段: " . implode(', ', $existingColumns) . "\n";
    
    // 需要添加的字段
    $requiredFields = [
        'su_fz_money' => "ALTER TABLE `web_substation` ADD COLUMN `su_fz_money` decimal(10,2) DEFAULT 0.00 COMMENT '分站资金'",
        'su_s_id' => "ALTER TABLE `web_substation` ADD COLUMN `su_s_id` int(11) DEFAULT 0 COMMENT '上级分站ID'",
        'su_endtime' => "ALTER TABLE `web_substation` ADD COLUMN `su_endtime` date DEFAULT NULL COMMENT '到期时间'",
        'su_name' => "ALTER TABLE `web_substation` ADD COLUMN `su_name` varchar(50) DEFAULT '' COMMENT '分站账号别名'",
        'su_pass' => "ALTER TABLE `web_substation` ADD COLUMN `su_pass` varchar(64) DEFAULT '' COMMENT '分站密码别名'",
        'su_dk' => "ALTER TABLE `web_substation` ADD COLUMN `su_dk` decimal(10,2) DEFAULT 0.00 COMMENT '点卡余额'",
        'su_dk_cd' => "ALTER TABLE `web_substation` ADD COLUMN `su_dk_cd` decimal(5,2) DEFAULT 0.00 COMMENT '抽佣比例'"
    ];
    
    foreach ($requiredFields as $field => $sql) {
        if (!in_array($field, $existingColumns)) {
            echo "添加字段: {$field}...\n";
            Db::execute($sql);
            echo "✅ 字段 {$field} 添加成功\n";
        } else {
            echo "⏭️ 字段 {$field} 已存在，跳过\n";
        }
    }
    
    // 同步数据到别名字段
    echo "\n3. 同步分站表数据...\n";
    $updateCount1 = Db::execute("UPDATE `web_substation` SET `su_name` = `su_username` WHERE `su_name` = '' OR `su_name` IS NULL");
    $updateCount2 = Db::execute("UPDATE `web_substation` SET `su_pass` = `su_password` WHERE `su_pass` = '' OR `su_pass` IS NULL");
    echo "✅ 同步了 {$updateCount1} 条分站账号数据\n";
    echo "✅ 同步了 {$updateCount2} 条分站密码数据\n";
    
    // 4. 检查分销表结构
    echo "\n4. 检查分销表结构...\n";
    $distributionColumns = Db::query("SHOW COLUMNS FROM web_distribution");
    $existingDistColumns = array_column($distributionColumns, 'Field');
    
    echo "当前分销表字段: " . implode(', ', $existingDistColumns) . "\n";
    
    // 需要添加的分销表字段
    $requiredDistFields = [
        'du_name' => "ALTER TABLE `web_distribution` ADD COLUMN `du_name` varchar(50) DEFAULT '' COMMENT '分销账号别名'",
        'du_pass' => "ALTER TABLE `web_distribution` ADD COLUMN `du_pass` varchar(64) DEFAULT '' COMMENT '分销密码别名'",
        'du_id' => "ALTER TABLE `web_distribution` ADD COLUMN `du_id` int(11) DEFAULT 0 COMMENT '分销ID别名'",
        'su_id' => "ALTER TABLE `web_distribution` ADD COLUMN `su_id` int(11) DEFAULT 0 COMMENT '所属分站ID别名'",
        'du_status' => "ALTER TABLE `web_distribution` ADD COLUMN `du_status` tinyint(1) DEFAULT 1 COMMENT '分销状态别名'"
    ];
    
    foreach ($requiredDistFields as $field => $sql) {
        if (!in_array($field, $existingDistColumns)) {
            echo "添加字段: {$field}...\n";
            Db::execute($sql);
            echo "✅ 字段 {$field} 添加成功\n";
        } else {
            echo "⏭️ 字段 {$field} 已存在，跳过\n";
        }
    }
    
    // 同步分销表数据
    echo "\n5. 同步分销表数据...\n";
    $updateCount3 = Db::execute("UPDATE `web_distribution` SET `du_name` = `di_username` WHERE `du_name` = '' OR `du_name` IS NULL");
    $updateCount4 = Db::execute("UPDATE `web_distribution` SET `du_pass` = `di_password` WHERE `du_pass` = '' OR `du_pass` IS NULL");
    $updateCount5 = Db::execute("UPDATE `web_distribution` SET `du_id` = `di_id` WHERE `du_id` = 0 OR `du_id` IS NULL");
    $updateCount6 = Db::execute("UPDATE `web_distribution` SET `su_id` = `di_su_id` WHERE `su_id` = 0 OR `su_id` IS NULL");
    $updateCount7 = Db::execute("UPDATE `web_distribution` SET `du_status` = `di_status` WHERE `du_status` IS NULL");
    
    echo "✅ 同步了 {$updateCount3} 条分销账号数据\n";
    echo "✅ 同步了 {$updateCount4} 条分销密码数据\n";
    echo "✅ 同步了 {$updateCount5} 条分销ID数据\n";
    echo "✅ 同步了 {$updateCount6} 条分站关联数据\n";
    echo "✅ 同步了 {$updateCount7} 条分销状态数据\n";
    
    // 6. 检查并创建微信群表
    echo "\n6. 检查微信群表...\n";
    $wxgroupExists = Db::query("SHOW TABLES LIKE 'web_wxgroup'");
    if (empty($wxgroupExists)) {
        echo "创建微信群表...\n";
        $createWxgroupSql = "
        CREATE TABLE `web_wxgroup` (
            `wxg_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '微信群ID',
            `su_id` int(11) NOT NULL DEFAULT 0 COMMENT '所属分站ID',
            `du_id` int(11) NOT NULL DEFAULT 0 COMMENT '所属分销ID',
            `wxg_title` varchar(100) NOT NULL COMMENT '群名称',
            `wxg_qrcode` varchar(255) DEFAULT '' COMMENT '群二维码',
            `wxg_count` int(11) DEFAULT 0 COMMENT '群人数',
            `wxg_max_count` int(11) DEFAULT 500 COMMENT '群最大人数',
            `wxg_status` tinyint(1) DEFAULT 1 COMMENT '状态 1:正常 2:满员 3:禁用',
            `wxg_addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
            `wxg_updatetime` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`wxg_id`),
            KEY `idx_su_id` (`su_id`),
            KEY `idx_du_id` (`du_id`),
            KEY `idx_status` (`wxg_status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信群表'";
        
        Db::execute($createWxgroupSql);
        echo "✅ 微信群表创建成功\n";
    } else {
        echo "⏭️ 微信群表已存在，跳过\n";
    }
    
    // 7. 验证修复结果
    echo "\n7. 验证修复结果...\n";
    
    // 检查分站表字段
    $newSubstationColumns = Db::query("SHOW COLUMNS FROM web_substation");
    $newSubColumns = array_column($newSubstationColumns, 'Field');
    
    $missingSubFields = array_diff(array_keys($requiredFields), $newSubColumns);
    if (empty($missingSubFields)) {
        echo "✅ 分站表字段修复完成\n";
    } else {
        echo "❌ 分站表仍缺少字段: " . implode(', ', $missingSubFields) . "\n";
    }
    
    // 检查分销表字段
    $newDistributionColumns = Db::query("SHOW COLUMNS FROM web_distribution");
    $newDistColumns = array_column($newDistributionColumns, 'Field');
    
    $missingDistFields = array_diff(array_keys($requiredDistFields), $newDistColumns);
    if (empty($missingDistFields)) {
        echo "✅ 分销表字段修复完成\n";
    } else {
        echo "❌ 分销表仍缺少字段: " . implode(', ', $missingDistFields) . "\n";
    }
    
    // 8. 测试查询
    echo "\n8. 测试修复后的查询...\n";
    
    // 测试分站查询
    try {
        $substationTest = Db::name('substation')->where('su_id', '>', 0)->limit(1)->find();
        if ($substationTest) {
            echo "✅ 分站表查询测试成功\n";
            echo "测试数据: su_id={$substationTest['su_id']}, su_name={$substationTest['su_name']}\n";
        } else {
            echo "⚠️ 分站表无数据，但查询结构正常\n";
        }
    } catch (\Exception $e) {
        echo "❌ 分站表查询测试失败: " . $e->getMessage() . "\n";
    }
    
    // 测试分销查询
    try {
        $distributionTest = Db::name('distribution')->where('di_id', '>', 0)->limit(1)->find();
        if ($distributionTest) {
            echo "✅ 分销表查询测试成功\n";
            echo "测试数据: du_id={$distributionTest['du_id']}, du_name={$distributionTest['du_name']}\n";
        } else {
            echo "⚠️ 分销表无数据，但查询结构正常\n";
        }
    } catch (\Exception $e) {
        echo "❌ 分销表查询测试失败: " . $e->getMessage() . "\n";
    }
    
    echo "\n=== 数据库修复脚本执行完成 ===\n";
    echo "✅ 所有修复操作已完成！\n";
    echo "📋 修复总结:\n";
    echo "   - 分站表字段修复完成\n";
    echo "   - 分销表字段修复完成\n";
    echo "   - 微信群表检查完成\n";
    echo "   - 数据同步完成\n";
    echo "   - 查询测试完成\n";
    
} catch (\Exception $e) {
    echo "❌ 修复过程中出现错误: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . "\n";
    echo "错误行号: " . $e->getLine() . "\n";
    exit(1);
}
